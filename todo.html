<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lista de Tarefas Avançada v2</title>
    <style>
        :root {
            --primary-bg: #1a1a1a; /* Even darker background */
            --secondary-bg: #2c2c2c; /* Container background */
            --tertiary-bg: #383838; /* Column background */
            --item-bg: #252525; /* Task item background */
            --item-hover-bg: #333333;
            --text-color: #e8e8e8;
            --accent-color: #00aeff; /* Brighter blue */
            --heading-color: #4dd0e1; /* Cyan for headings */
            --border-color: #4a4a4a;
            --shadow-color: rgba(0, 0, 0, 0.5);
            --danger-color: #ff4d4d;
            --warning-color: #ffaa00;
            --success-color: #4caf50;
            --tag-bg: #555;
            --tag-text: #f0f0f0;
        }

        body {
            font-family: '<PERSON>o', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            background-color: var(--primary-bg);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            width: 98%;
            max-width: 1300px;
            background-color: var(--secondary-bg);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px var(--shadow-color);
        }

        h1 {
            text-align: center;
            color: var(--heading-color);
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
            letter-spacing: 1px;
        }

        .task-input-area {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 35px;
            align-items: center;
        }

        .task-input-area input[type="text"],
        .task-input-area input[type="datetime-local"] {
            padding: 14px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--tertiary-bg);
            color: var(--text-color);
            font-size: 1em;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .task-input-area input[type="text"]::placeholder {
            color: #888;
        }
        .task-input-area input[type="text"]:focus,
        .task-input-area input[type="datetime-local"]:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(0, 174, 255, 0.3);
        }


        .add-btn {
            padding: 14px 22px;
            background: linear-gradient(145deg, var(--accent-color), #007acc);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: background 0.3s ease, transform 0.2s ease;
            grid-column: span 1; /* Ensure it takes its space */
        }
        @media (min-width: 769px) { /* For larger screens, make button span less if many inputs */
            .task-input-area.many-inputs .add-btn {  /* Add 'many-inputs' class via JS if needed */
                 grid-column: span 1 / -1; /* Span to the end */
            }
        }


        .add-btn:hover {
            background: linear-gradient(145deg, #007acc, var(--accent-color));
            transform: translateY(-2px);
        }
        .add-btn:active {
            transform: translateY(0px);
        }

        .task-columns {
            display: flex;
            justify-content: space-between;
            gap: 25px;
            flex-wrap: wrap;
        }

        .task-column {
            background-color: var(--tertiary-bg);
            border-radius: 10px;
            padding: 20px;
            width: calc(33.333% - 25px);
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .task-column h2 {
            text-align: center;
            color: var(--text-color);
            margin-top: 0;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 12px;
            font-size: 1.5em;
            font-weight: 400;
        }

        .task-column ul {
            list-style-type: none;
            padding: 0;
            min-height: 200px;
            flex-grow: 1; /* Make ul take available space */
            border: 2px dashed transparent; /* For drop indication */
            border-radius: 6px;
            transition: border-color 0.3s ease;
        }
        .task-column ul.drag-over {
            border-color: var(--accent-color);
        }


        .task-item {
            background-color: var(--item-bg);
            padding: 18px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 6px solid var(--accent-color);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
            cursor: grab;
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
        }
        
        .task-item:hover {
            background-color: var(--item-hover-bg);
            box-shadow: 0 5px 12px rgba(0, 0, 0, 0.35);
        }

        .task-item.dragging {
            opacity: 0.6;
            transform: rotate(2deg) scale(1.01);
            box-shadow: 0 8px 20px var(--shadow-color);
        }

        .task-item .task-content p {
            margin: 0 0 10px 0;
            font-size: 1.15em;
            word-break: break-word;
        }
        
        .task-item .task-details {
            font-size: 0.9em;
            color: #b0b0b0;
            margin-bottom: 12px;
        }
        .task-item .task-details span {
            display: block;
            margin-bottom: 4px;
        }

        .task-item .task-timer {
            font-size: 0.95em;
            color: var(--warning-color);
            margin-bottom: 10px;
            font-weight: 500;
        }

        .task-tags {
            margin-bottom: 10px;
        }
        .task-tags .tag {
            display: inline-block;
            background-color: var(--tag-bg);
            color: var(--tag-text);
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-right: 6px;
            margin-bottom: 6px;
        }

        .task-item .actions button {
            padding: 8px 12px;
            margin-right: 10px;
            margin-top: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        .task-item .actions button:hover {
            transform: translateY(-1px);
        }

        .task-item .actions .remove-btn {
            background-color: var(--danger-color);
            color: white;
        }
        .task-item .actions .remove-btn:hover {
            background-color: #e03c3c;
        }

        .task-item .actions .move-btn {
            background-color: var(--warning-color);
            color: var(--primary-bg);
        }
        .task-item .actions .move-btn:hover {
            background-color: #e69500;
        }
        
        .task-item .actions .complete-btn {
            background-color: var(--success-color);
            color: white;
        }
        .task-item .actions .complete-btn:hover {
            background-color: #43a047;
        }

        @media (max-width: 1200px) {
            .task-column {
                width: calc(50% - 20px);
            }
        }
        @media (max-width: 768px) {
            .task-input-area {
                grid-template-columns: 1fr; /* Stack inputs */
            }
            .add-btn {
                width: 100%;
            }
            .task-column {
                width: 100%;
                margin-bottom: 25px;
            }
            .task-columns {
                flex-direction: column;
            }
            h1 {
                font-size: 2em;
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Quadro de Tarefas Dinâmico</h1>

        <div class="task-input-area">
            <input type="text" id="taskInput" placeholder="Descrição da tarefa...">
            <input type="text" id="tagsInput" placeholder="Tags (ex: trabalho,pessoal)">
            <input type="datetime-local" id="startTimeInput" title="Horário de Início">
            <input type="datetime-local" id="endTimeInput" title="Horário de Fim">
            <button onclick="addTask()" class="add-btn">Adicionar Tarefa</button>
        </div>

        <div class="task-columns">
            <div class="task-column" id="todoColumn">
                <h2><span class="icon">📋</span> A Fazer</h2>
                <ul id="todoList" ondragover="allowDrop(event)" ondrop="drop(event, 'todoList')" ondragenter="handleDragEnter(event)" ondragleave="handleDragLeave(event)"></ul>
            </div>
            <div class="task-column" id="inProgressColumn">
                <h2><span class="icon">⚙️</span> Em Andamento</h2>
                <ul id="inProgressList" ondragover="allowDrop(event)" ondrop="drop(event, 'inProgressList')" ondragenter="handleDragEnter(event)" ondragleave="handleDragLeave(event)"></ul>
            </div>
            <div class="task-column" id="completedColumn">
                <h2><span class="icon">✔️</span> Concluído</h2>
                <ul id="completedList" ondragover="allowDrop(event)" ondrop="drop(event, 'completedList')" ondragenter="handleDragEnter(event)" ondragleave="handleDragLeave(event)"></ul>
            </div>
        </div>
    </div>

    <script>
        let taskIdCounter = 0;
        const taskTimers = {}; // Store intervals for timers

        function addTask() {
            const taskInput = document.getElementById('taskInput');
            const startTimeInput = document.getElementById('startTimeInput');
            const endTimeInput = document.getElementById('endTimeInput');
            const tagsInput = document.getElementById('tagsInput');

            const taskText = taskInput.value.trim();
            const startTime = startTimeInput.value;
            const endTime = endTimeInput.value;
            const tags = tagsInput.value.trim().split(',').map(tag => tag.trim()).filter(tag => tag !== '');

            if (taskText === "") {
                alert("Por favor, insira a descrição da tarefa.");
                return;
            }

            const taskList = document.getElementById('todoList');
            const taskId = `task-${taskIdCounter++}`;
            const listItem = createTaskElement(taskText, startTime, endTime, tags, taskId);
            taskList.appendChild(listItem);

            taskInput.value = "";
            startTimeInput.value = "";
            endTimeInput.value = "";
            tagsInput.value = "";
        }

        function createTaskElement(taskText, startTime, endTime, tags, id) {
            const listItem = document.createElement('li');
            listItem.className = 'task-item';
            listItem.id = id;
            listItem.draggable = true;
            listItem.ondragstart = drag;

            const taskContentDiv = document.createElement('div');
            taskContentDiv.className = 'task-content';
            
            const taskP = document.createElement('p');
            taskP.textContent = taskText;
            taskContentDiv.appendChild(taskP);

            const taskDetailsDiv = document.createElement('div');
            taskDetailsDiv.className = 'task-details';
            if (startTime) {
                const startSpan = document.createElement('span');
                startSpan.textContent = `Início: ${formatDateTime(startTime)}`;
                taskDetailsDiv.appendChild(startSpan);
            }
            if (endTime) {
                const endSpan = document.createElement('span');
                endSpan.textContent = `Fim: ${formatDateTime(endTime)}`;
                taskDetailsDiv.appendChild(endSpan);
            }
             if (!startTime && !endTime) {
                const noTimeSpan = document.createElement('span');
                noTimeSpan.textContent = 'Sem horários definidos';
                taskDetailsDiv.appendChild(noTimeSpan);
            }

            const tagsDiv = document.createElement('div');
            tagsDiv.className = 'task-tags';
            tags.forEach(tagText => {
                const tagSpan = document.createElement('span');
                tagSpan.className = 'tag';
                tagSpan.textContent = tagText;
                tagsDiv.appendChild(tagSpan);
            });

            const timerDiv = document.createElement('div');
            timerDiv.className = 'task-timer';
            timerDiv.id = `timer-${id}`; // Unique ID for the timer display

            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'actions';

            listItem.appendChild(taskContentDiv);
            listItem.appendChild(tagsDiv);
            listItem.appendChild(taskDetailsDiv);
            listItem.appendChild(timerDiv); // Add timer placeholder
            listItem.appendChild(actionsDiv);
            
            updateActionButtons(listItem, 'todoList');
            return listItem;
        }

        function updateActionButtons(listItem, targetListId) {
            const actionsDiv = listItem.querySelector('.actions');
            actionsDiv.innerHTML = ''; 
            const taskId = listItem.id;

            stopTimer(taskId); // Stop any existing timer for this task

            const removeButton = document.createElement('button');
            removeButton.textContent = "Remover";
            removeButton.className = "remove-btn";
            removeButton.onclick = function() { 
                stopTimer(taskId);
                listItem.remove(); 
            };
            actionsDiv.appendChild(removeButton);

            if (targetListId === 'todoList') {
                const moveToInProgressButton = document.createElement('button');
                moveToInProgressButton.textContent = "Iniciar";
                moveToInProgressButton.className = "move-btn";
                moveToInProgressButton.onclick = function() {
                    document.getElementById('inProgressList').appendChild(listItem);
                    updateActionButtons(listItem, 'inProgressList');
                };
                actionsDiv.appendChild(moveToInProgressButton);
            } else if (targetListId === 'inProgressList') {
                startTimer(listItem); // Start timer when moved to "In Progress"

                const moveToTodoButton = document.createElement('button');
                moveToTodoButton.textContent = "Para Fazer";
                moveToTodoButton.className = "move-btn";
                moveToTodoButton.style.backgroundColor = '#868e96'; 
                moveToTodoButton.onmouseover = function() { this.style.backgroundColor = '#6c757d';}
                moveToTodoButton.onmouseout = function() { this.style.backgroundColor = '#868e96';}
                moveToTodoButton.onclick = function() {
                    document.getElementById('todoList').appendChild(listItem);
                    updateActionButtons(listItem, 'todoList');
                };
                actionsDiv.appendChild(moveToTodoButton);

                const moveToCompletedButton = document.createElement('button');
                moveToCompletedButton.textContent = "Concluir";
                moveToCompletedButton.className = "complete-btn";
                moveToCompletedButton.onclick = function() {
                    document.getElementById('completedList').appendChild(listItem);
                    updateActionButtons(listItem, 'completedList');
                };
                actionsDiv.appendChild(moveToCompletedButton);
            } else if (targetListId === 'completedList') {
                listItem.querySelector(`#timer-${taskId}`).textContent = ''; // Clear timer text
                const moveToInProgressButton = document.createElement('button');
                moveToInProgressButton.textContent = "Reabrir";
                moveToInProgressButton.className = "move-btn";
                moveToInProgressButton.onclick = function() {
                    document.getElementById('inProgressList').appendChild(listItem);
                    updateActionButtons(listItem, 'inProgressList');
                };
                actionsDiv.appendChild(moveToInProgressButton);
            }
        }

        function startTimer(listItem) {
            const taskId = listItem.id;
            const timerDisplay = listItem.querySelector(`#timer-${taskId}`);
            if (!timerDisplay) return;

            stopTimer(taskId); // Ensure no duplicate timers

            // Store the time when the task was moved to "In Progress"
            // If it was already started and moved back, it might have this attribute
            if (!listItem.dataset.startTimeEpoch) {
                 listItem.dataset.startTimeEpoch = Date.now();
            }
           
            taskTimers[taskId] = setInterval(() => {
                const startTimeEpoch = parseInt(listItem.dataset.startTimeEpoch, 10);
                const elapsedTime = Date.now() - startTimeEpoch;
                timerDisplay.textContent = `Em progresso: ${formatDuration(elapsedTime)}`;
            }, 1000);
        }

        function stopTimer(taskId) {
            if (taskTimers[taskId]) {
                clearInterval(taskTimers[taskId]);
                delete taskTimers[taskId];
                // Don't clear listItem.dataset.startTimeEpoch here,
                // as it might be moved back to "In Progress" later.
                // Clear the display text if not in "In Progress"
                const listItem = document.getElementById(taskId);
                if (listItem && listItem.parentNode && listItem.parentNode.id !== 'inProgressList') {
                    const timerDisplay = listItem.querySelector(`#timer-${taskId}`);
                    if (timerDisplay) timerDisplay.textContent = '';
                }
            }
        }
        
        function formatDuration(ms) {
            let seconds = Math.floor(ms / 1000);
            let minutes = Math.floor(seconds / 60);
            let hours = Math.floor(minutes / 60);

            seconds = seconds % 60;
            minutes = minutes % 60;

            return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        }


        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('pt-BR', { 
                day: '2-digit', month: '2-digit', year: 'numeric', 
                hour: '2-digit', minute: '2-digit' 
            });
        }

        function allowDrop(ev) {
            ev.preventDefault();
        }

        function drag(ev) {
            ev.dataTransfer.setData("text", ev.target.id);
            ev.target.classList.add('dragging');
        }

        function drop(ev, targetListId) {
            ev.preventDefault();
            const data = ev.dataTransfer.getData("text");
            const draggedElement = document.getElementById(data);
            const targetListElement = document.getElementById(targetListId);
            
            handleDragLeave({target: targetListElement}); // Remove drag-over class

            if (draggedElement && targetListElement && draggedElement.parentNode !== targetListElement) {
                targetListElement.appendChild(draggedElement);
                updateActionButtons(draggedElement, targetListId);
            }
            if (draggedElement) {
                draggedElement.classList.remove('dragging');
            }
        }
        
        function handleDragEnter(ev) {
            if (ev.target.classList.contains('task-column') || ev.target.closest('ul')) {
                 (ev.target.closest('ul') || ev.target).classList.add('drag-over');
            }
        }

        function handleDragLeave(ev) {
             if (ev.target.classList.contains('task-column') || ev.target.closest('ul')) {
                 (ev.target.closest('ul') || ev.target).classList.remove('drag-over');
            }
        }


        document.addEventListener('DOMContentLoaded', () => {
            // Example tasks for demonstration
            // const todoList = document.getElementById('todoList');
            // todoList.appendChild(createTaskElement('Revisar Documentação UX', '2024-05-28T09:00', '2024-05-28T10:30', ['doc', 'ux'], `task-${taskIdCounter++}`));
            // todoList.appendChild(createTaskElement('Planejar Sprint Semanal', '', '', ['planejamento', 'equipe'], `task-${taskIdCounter++}`));
            
            // const inProgressList = document.getElementById('inProgressList');
            // const devTask = createTaskElement('Desenvolver API de Pagamentos', '2024-05-27T14:00', '2024-05-29T18:00', ['dev', 'api', 'urgente'], `task-${taskIdCounter++}`);
            // inProgressList.appendChild(devTask);
            // updateActionButtons(devTask, 'inProgressList'); // This will also start its timer
        });

    </script>
</body>
</html>