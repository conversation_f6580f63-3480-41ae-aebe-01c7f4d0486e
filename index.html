<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Dashboard de Métricas do Facebook</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    /* Insira todo o código CSS aqui */
    :root {
  --bg-dark: #181c24;
  --bg-card: #232a38;
  --bg-card-light: #25304a;
  --bg-sidebar: #1e2532;
  --primary: #3b82f6;
  --primary-dark: #2563eb;
  --text: #e2e8f0;
  --text-light: #94a3b8;
  --text-muted: #64748b;
  --border: #374151;
  --success: #10b981;
  --warning: #f59e0b;
  --info: #3b82f6;
  --danger: #ef4444;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--bg-dark);
  color: var(--text);
  font-family: 'Segoe UI', Arial, sans-serif;
  line-height: 1.5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: var(--bg-sidebar);
  border-bottom: 1px solid var(--border);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.18);
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo i {
  color: var(--primary);
  font-size: 2rem;
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text);
  letter-spacing: 0.03em;
}

.logo-subtitle {
  font-size: 0.85rem;
  color: var(--text-light);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.2rem;
}

.client-selector {
  background: var(--bg-card);
  border: 1.5px solid var(--border);
  color: var(--text);
  padding: 0.6rem 1.2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.client-selector:hover,
.client-selector:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(59,130,246,0.10);
}

.theme-toggle {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  font-size: 1.5rem;
  border-radius: 50%;
  padding: 0.4rem;
  transition: background 0.2s, color 0.2s;
}

.theme-toggle:hover {
  color: var(--primary);
  background: rgba(59,130,246,0.07);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2.5rem 1.5rem;
}

.filter-card {
  background: var(--bg-card-light);
  border-radius: 0.75rem;
  padding: 2rem 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 6px 18px rgba(59,130,246,0.10), 0 2px 8px rgba(0,0,0,0.10);
  transition: transform 0.3s, box-shadow 0.3s;
}

.filter-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 24px rgba(59,130,246,0.13), 0 4px 16px rgba(0,0,0,0.13);
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-bottom: 1.2rem;
  color: var(--primary);
  font-weight: 700;
  font-size: 1.1rem;
}

.filter-toggle {
  margin-left: auto;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s, transform 0.3s;
}

.filter-toggle:hover {
  background: rgba(59,130,246,0.07);
}

.filter-toggle i {
  color: var(--text-light);
  font-size: 1.1rem;
  transition: color 0.2s;
}

.filter-toggle:hover i {
  color: var(--primary);
}

.filter-toggle.collapsed i {
  transform: rotate(180deg);
}

.filter-content {
  max-height: 1000px;
  overflow: hidden;
  transition: max-height 0.5s;
}

.filter-content.collapsed {
  max-height: 0;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 1.2rem;
  margin-bottom: 1.2rem;
}

.form-group {
  flex: 1;
  min-width: 220px;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  color: var(--text-light);
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.7rem;
  background: var(--bg-dark);
  border: 1.5px solid var(--border);
  border-radius: 0.5rem;
  color: var(--text);
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
  border-color: var(--primary-dark);
  outline: none;
  box-shadow: 0 0 0 2px rgba(59,130,246,0.12);
}

.filter-info {
  text-align: center;
  color: var(--text-light);
  font-size: 0.95rem;
  margin: 1.2rem 0;
}

.btn-group {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.btn {
  padding: 0.6rem 1.3rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.7rem;
  border: none;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.2s;
}

.btn-outline {
  background: transparent;
  border: 1.5px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background: var(--primary);
  color: #fff;
  box-shadow: 0 4px 16px rgba(59,130,246,0.15);
}

.btn-primary {
  background: linear-gradient(90deg, var(--primary), var(--primary-dark));
  color: #fff;
  border: none;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px) scale(1.03);
  box-shadow: 0 4px 16px rgba(59,130,246,0.13);
}

.tabs {
  display: flex;
  border-bottom: 1.5px solid var(--border);
  margin-bottom: 2rem;
  overflow-x: auto;
  gap: 0.5rem;
}

.tab {
  padding: 0.9rem 2rem;
  color: var(--text-light);
  cursor: pointer;
  white-space: nowrap;
  font-size: 1.05rem;
  font-weight: 600;
  border-radius: 0.5rem 0.5rem 0 0;
  background: none;
  transition: color 0.2s, background 0.2s;
}

.tab:hover {
  color: var(--primary);
  background: rgba(59,130,246,0.06);
}

.tab.active {
  color: var(--primary);
  background: var(--bg-card-light);
  border-bottom: 2.5px solid var(--primary);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: var(--bg-card);
  border-radius: 0.75rem;
  padding: 2rem 1.2rem;
  box-shadow: 0 6px 18px rgba(59,130,246,0.10), 0 2px 8px rgba(0,0,0,0.10);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  border: 1.5px solid var(--border);
}

.metric-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 16px 32px rgba(59,130,246,0.15), 0 4px 16px rgba(0,0,0,0.13);
  background: linear-gradient(120deg, var(--bg-card-light) 80%, var(--primary) 100%);
}

.metric-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: var(--primary);
  transition: width 0.4s;
}

.metric-card:hover::after {
  width: 100%;
}

.metric-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  margin-bottom: 1rem;
  font-size: 2rem;
  transition: transform 0.3s;
}

.metric-card:hover .metric-icon {
  transform: scale(1.15) rotate(-8deg);
}

.icon-blue {
  background: rgba(59, 130, 246, 0.13);
  color: var(--info);
}

.icon-green {
  background: rgba(16, 185, 129, 0.13);
  color: var(--success);
}

.icon-orange {
  background: rgba(245, 158, 11, 0.13);
  color: var(--warning);
}

.icon-purple {
  background: rgba(139, 92, 246, 0.13);
  color: #8b5cf6;
}

.icon-red {
  background: rgba(239, 68, 68, 0.13);
  color: var(--danger);
}

.metric-value {
  font-size: 2.2rem;
  font-weight: 800;
  margin-bottom: 0.3rem;
}

.metric-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-light);
  margin-bottom: 0.5rem;
  letter-spacing: 0.02em;
}

.metric-info {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.chart-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (max-width: 900px) {
  .chart-container {
    grid-template-columns: 1fr;
  }
}

.chart-card {
  background: var(--bg-card);
  border-radius: 0.75rem;
  padding: 2rem 1.2rem;
  box-shadow: 0 6px 18px rgba(59,130,246,0.10);
  min-height: 300px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.chart-card:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 12px 24px rgba(59,130,246,0.13);
}
.chart-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

#conversion-trend-chart {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 250px;
}

#conversion-trend-canvas {
  width: 100% !important;
  height: 100% !important;
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.6rem;
  letter-spacing: 0.01em;
}

.chart-placeholder {
  height: 250px;
  background: var(--bg-dark);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  font-size: 1.1rem;
}

.comparison-chart-container {
  background: var(--bg-card);
  border-radius: 0.75rem;
  padding: 2rem 1.5rem;
  box-shadow: 0 6px 18px rgba(59,130,246,0.10);
  margin-bottom: 2rem;
  transition: transform 0.3s, box-shadow 0.3s;
}

.comparison-chart-container:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 12px 24px rgba(59,130,246,0.13);
}

.comparison-chart-title {
  font-size: 1.15rem;
  font-weight: 700;
  margin-bottom: 1.1rem;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.6rem;
}

.comparison-chart {
  height: 300px;
  background: var(--bg-dark);
  border-radius: 0.5rem;
  padding: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.2rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  font-size: 1rem;
  color: var(--text-light);
}

.legend-color {
  width: 15px;
  height: 15px;
  border-radius: 3px;
}

.legend-current {
  background-color: var(--primary);
}

.legend-previous {
  background-color: var(--warning);
}

.tooltip-container {
  position: relative;
  display: inline-block;
  margin-left: 0.7rem;
  cursor: pointer;
}

.tooltip-icon {
  color: var(--text-light);
  font-size: 1rem;
  transition: color 0.2s;
}

.tooltip-container:hover .tooltip-icon {
  color: var(--primary);
}

.tooltip-text {
  visibility: hidden;
  width: 250px;
  background-color: var(--bg-sidebar);
  color: var(--text);
  text-align: left;
  border-radius: 0.5rem;
  padding: 1rem;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -125px;
  opacity: 0;
  transition: opacity 0.3s;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border);
  font-size: 0.95rem;
  line-height: 1.5;
}

.tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--bg-sidebar) transparent transparent transparent;
}

.tooltip-container:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

.tooltip-title {
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.5rem;
  display: block;
}

.leads-section {
  background: var(--bg-card);
  border-radius: 0.75rem;
  padding: 2rem 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 6px 18px rgba(59,130,246,0.10);
}

.leads-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1.5px solid var(--border);
  padding-bottom: 1rem;
}

.leads-section-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.7rem;
}

.leads-detail-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.leads-detail-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.1rem 0.7rem;
  border-bottom: 1.5px solid var(--border);
  transition: background 0.2s, transform 0.2s;
}

.leads-detail-list li:hover {
  background: rgba(59, 130, 246, 0.06);
  transform: translateX(6px);
}

.leads-detail-list li:last-child {
  border-bottom: none;
}

.lead-detail-type {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.lead-detail-icon {
  width: 2.7rem;
  height: 2.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.13);
  color: var(--primary);
  font-size: 1.5rem;
  transition: transform 0.2s;
}

.leads-detail-list li:hover .lead-detail-icon {
  transform: scale(1.13);
}

.lead-detail-info {
  display: flex;
  flex-direction: column;
}

.lead-detail-name {
  font-weight: 600;
  font-size: 1.05rem;
}

.lead-detail-source {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.lead-detail-count {
  font-size: 1.35rem;
  font-weight: 700;
  color: var(--text);
  background: var(--bg-dark);
  padding: 0.3rem 1.1rem;
  border-radius: 1.2rem;
  min-width: 3.2rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.12);
}

.lead-progress-container {
  width: 100%;
  height: 5px;
  background: var(--bg-dark);
  border-radius: 3px;
  margin-top: 0.5rem;
}

.lead-progress-bar {
  height: 100%;
  border-radius: 3px;
  background: var(--primary);
  transition: width 0.5s;
}

.leads-chart {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 900px) {
  .leads-chart {
    grid-template-columns: 1fr;
  }
}

.chart-area {
  background: var(--bg-dark);
  border-radius: 0.75rem;
  padding: 2rem 1.2rem;
  min-height: 250px;
  box-shadow: 0 4px 12px rgba(59,130,246,0.08);
  transition: transform 0.3s;
}

.chart-area:hover {
  transform: translateY(-5px) scale(1.01);
}

.loading-spinner {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0,0,0,0.7);
  z-index: 9999;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 6px solid #f3f3f3;
  border-top: 6px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}

@media (max-width: 700px) {
  .container {
    padding: 1rem 0.5rem;
  }
  .metrics-grid, .chart-container, .leads-chart {
    gap: 1rem;
  }
  .metric-card, .chart-card, .conversion-metrics, .leads-section {
    padding: 1rem;
  }
}

@media (max-width: 600px) {
  .filter-form {
    flex-direction: column;
  }
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  .chart-container, .leads-chart {
    grid-template-columns: 1fr;
  }
}

/* Animações adicionais */
@keyframes pulse {
  0% { transform: scale(1);}
  50% { transform: scale(1.05);}
  100% { transform: scale(1);}
}
.pulse {
  animation: pulse 2s infinite;
}

/* Métricas de conversão */
.conversion-metrics {
  background: var(--bg-card);
  border-radius: 0.75rem;
  padding: 2rem 1.2rem;
  box-shadow: 0 6px 18px rgba(59,130,246,0.10);
  transition: transform 0.3s, box-shadow 0.3s;
}

.conversion-metrics:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 12px 24px rgba(59,130,246,0.13);
}

.conversion-title {
  font-size: 1.15rem;
  font-weight: 700;
  margin-bottom: 1.3rem;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.6rem;
}

.conversion-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.3rem;
}

.conversion-item {
  background: var(--bg-dark);
  border-radius: 0.5rem;
  padding: 1.1rem;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  border: 1.5px solid var(--border);
}

.conversion-item:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 6px 12px rgba(59,130,246,0.13);
}

.conversion-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: var(--primary);
  transition: width 0.4s;
}

.conversion-item:hover::after {
  width: 100%;
}

.conversion-label {
  font-size: 1rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.conversion-label i {
  font-size: 1.3rem;
  transition: transform 0.2s;
}

.conversion-item:hover .conversion-label i {
  transform: scale(1.13) rotate(-6deg);
}

.conversion-value {
  font-size: 1.6rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.conversion-change {
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.change-up {
  color: var(--success);
}

.change-down {
  color: var(--danger);
}

.comparison-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.6rem;
  border-radius: 0.3rem;
  font-size: 0.9rem;
  margin-left: 0.5rem;
  background: var(--bg-dark);
}

.comparison-up {
  color: var(--success);
}
.comparison-down {
  color: var(--danger);
}
.comparison-neutral {
  color: var(--text-light);
}
  </style>
</head>
<body>
  <div class="loading-spinner" id="loading-spinner">
    <div class="spinner"></div>
  </div>

  <header class="header">
    <div class="logo">
      <i class="fab fa-facebook"></i>
      <div class="logo-text">
        <span class="logo-title">Dashboard de Métricas do Facebook</span>
        <span class="logo-subtitle">Análise de performance de campanhas</span>
      </div>
    </div>
    <div class="header-right">
      <select class="client-selector">
        <option>Selecione um cliente</option>
      </select>
      <button class="theme-toggle" id="theme-toggle">
        <i class="fas fa-moon"></i>
      </button>
    </div>
  </header>

  <div class="container">
    <div class="filter-card">
      <div class="filter-title">
        <i class="fas fa-filter"></i>
        <span>Filtros</span>
        <div class="filter-toggle" id="filter-toggle">
          <i class="fas fa-chevron-up"></i>
        </div>
      </div>
      <div id="filter-content" class="filter-content">
        <form id="filterForm" class="filter-form">
          <div class="form-group">
            <label for="dataInicial">Data Início</label>
            <input type="date" id="dataInicial" class="form-control" required>
          </div>
          <div class="form-group">
            <label for="dataFinal">Data Fim</label>
            <input type="date" id="dataFinal" class="form-control" required>
          </div>
          <div class="form-group">
            <label for="objetivo">Objetivo</label>
            <select id="objetivo" class="form-control">
              <option value="todos">Todos os objetivos</option>
              <option value="conversao">Conversão</option>
              <option value="trafego">Tráfego</option>
              <option value="reconhecimento">Reconhecimento</option>
            </select>
          </div>
          <div class="form-group">
            <label for="comparacao">Comparar com</label>
            <select id="comparacao" class="form-control">
              <option value="nenhum">Sem comparação</option>
              <option value="periodo_anterior">Período anterior</option>
              <option value="mesmo_periodo_ano_anterior">Mesmo período do ano anterior</option>
            </select>
          </div>
        </form>
        <div class="filter-info" id="filter-info">Carregando dados dos últimos 90 dias...</div>
        <div class="btn-group">
          <button type="button" id="btnLimpar" class="btn btn-outline">
            <i class="fas fa-times"></i>
            Limpar
          </button>
          <button type="submit" form="filterForm" class="btn btn-primary">
            <i class="fas fa-search"></i>
            Aplicar Filtros
          </button>
        </div>
      </div>
    </div>

    <!-- Novo gráfico comparativo -->
    <div class="comparison-chart-container">
      <h3 class="comparison-chart-title">
        <i class="fas fa-chart-line"></i>
        Comparativo de Métricas
      </h3>
      <div class="comparison-chart" id="comparison-chart">
        <canvas id="comparison-chart-canvas"></canvas>
      </div>
      <div class="chart-legend">
        <div class="legend-item">
          <div class="legend-color legend-current"></div>
          <span>Período Atual</span>
        </div>
        <div class="legend-item">
          <div class="legend-color legend-previous"></div>
          <span>Período Anterior</span>
        </div>
      </div>
    </div>

    <div class="tabs">
      <div class="tab active" data-tab="overview">Visão Geral</div>
      <div class="tab" data-tab="campaigns">Campanhas</div>
      <div class="tab" data-tab="leads">Leads</div>
      <div class="tab" data-tab="audience">Audiência</div>
    </div>

    <div id="overview-tab" class="tab-content active">
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-icon icon-blue">
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div class="metric-value" id="total-spend">R$ 0,00</div>
          <div class="metric-title">Investimento Total</div>
          <div class="metric-info">
            Período selecionado
            <span class="comparison-badge" id="spend-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon icon-green">
            <i class="fas fa-user-plus"></i>
          </div>
          <div class="metric-value" id="total-leads">0</div>
          <div class="metric-title">Total de Leads</div>
          <div class="metric-info" id="leads-percent">
            0% de conversão
            <span class="comparison-badge" id="leads-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon icon-orange">
            <i class="fas fa-tags"></i>
          </div>
          <div class="metric-value" id="cpl">R$ 0,00</div>
          <div class="metric-title">Custo por Lead (CPL)</div>
          <div class="metric-info">
            Média
            <span class="comparison-badge" id="cpl-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon icon-purple">
            <i class="fas fa-mouse-pointer"></i>
          </div>
          <div class="metric-value" id="avg-ctr">0%</div>
          <div class="metric-title">CTR (Taxa de Cliques)</div>
          <div class="metric-info">
            Média
            <span class="comparison-badge" id="ctr-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
      </div>

      <div class="chart-container">
        <!-- Componente de métricas de conversão -->
        <div class="conversion-metrics">
          <h3 class="conversion-title">
            <i class="fas fa-exchange-alt"></i>
            Métricas de Conversão
          </h3>
          <div class="conversion-grid">
            <div class="conversion-item">
              <div class="conversion-label">
                <i class="fas fa-funnel-dollar"></i>
                Taxa de Conversão
              </div>
              <div class="conversion-value" id="conversion-rate">0%</div>
              <div class="conversion-change" id="conversion-rate-change">
                <i class="fas fa-minus"></i>
                <span>Sem comparação</span>
              </div>
            </div>
            <div class="conversion-item">
              <div class="conversion-label">
                <i class="fas fa-bullseye"></i>
                CPM Médio
              </div>
              <div class="conversion-value" id="avg-cpm">R$ 0,00</div>
              <div class="conversion-change" id="cpm-change">
                <i class="fas fa-minus"></i>
                <span>Sem comparação</span>
              </div>
            </div>
            <div class="conversion-item">
              <div class="conversion-label">
                <i class="fas fa-chart-line"></i>
                CPP Médio
              </div>
              <div class="conversion-value" id="avg-cpp">R$ 0,00</div>
              <div class="conversion-change" id="cpp-change">
                <i class="fas fa-minus"></i>
                <span>Sem comparação</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="chart-card">
          <h3 class="chart-title">
            <i class="fas fa-chart-line"></i>
            Tendência de Conversão
          </h3>
          <div id="conversion-trend-chart" style="position: relative; height: 100%; width: 100%;">
            <canvas id="conversion-trend-canvas"></canvas>
          </div>
        </div>
      </div>

      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-icon icon-blue">
            <i class="fas fa-eye"></i>
          </div>
          <div class="metric-value" id="total-impressions">0</div>
          <div class="metric-title">Impressões</div>
          <div class="metric-info">
            Total
            <span class="comparison-badge" id="impressions-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon icon-green">
            <i class="fas fa-mouse-pointer"></i>
          </div>
          <div class="metric-value" id="total-clicks">0</div>
          <div class="metric-title">Cliques</div>
          <div class="metric-info">
            Total
            <span class="comparison-badge" id="clicks-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon icon-orange">
            <i class="fas fa-hand-pointer"></i>
          </div>
          <div class="metric-value" id="total-link-clicks">0</div>
          <div class="metric-title">Cliques em Links</div>
          <div class="metric-info">
            Total
            <span class="comparison-badge" id="link-clicks-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon icon-purple">
            <i class="fas fa-users"></i>
          </div>
          <div class="metric-value" id="total-reach">0</div>
          <div class="metric-title">Alcance</div>
          <div class="metric-info">
            Total
            <span class="comparison-badge" id="reach-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
      </div>

      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-icon icon-blue">
            <i class="fas fa-hand-pointer"></i>
          </div>
          <div class="metric-value" id="avg-cpc">R$ 0,00</div>
          <div class="metric-title">CPC (Custo por Clique)</div>
          <div class="metric-info">
            Média
            <span class="comparison-badge" id="cpc-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon icon-green">
            <i class="fas fa-eye"></i>
          </div>
          <div class="metric-value" id="avg-cpm-card">R$ 0,00</div>
          <div class="metric-title">CPM (Custo por Mil Impressões)</div>
          <div class="metric-info">
            Média
            <span class="comparison-badge" id="cpm-card-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon icon-orange">
            <i class="fas fa-users"></i>
          </div>
          <div class="metric-value" id="avg-cpp-card">R$ 0,00</div>
          <div class="metric-title">CPP (Custo por Pessoa)</div>
          <div class="metric-info">
            Média
            <span class="comparison-badge" id="cpp-card-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
        <div class="metric-card">
          <div class="metric-icon icon-red">
            <i class="fas fa-percentage"></i>
          </div>
          <div class="metric-value" id="taxa-conversao-card">0%</div>
          <div class="metric-title">Taxa de Conversão</div>
          <div class="metric-info">
            Leads / Impressões
            <span class="comparison-badge" id="conversion-rate-card-comparison">
              <i class="fas fa-minus"></i>
              <span>Sem comparação</span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <div id="campaigns-tab" class="tab-content">
      <div class="filter-card">
        <h3 class="chart-title">
          <i class="fas fa-bullhorn"></i>
          Campanhas
        </h3>
        <div class="chart-placeholder">
          <span>Dados de campanhas serão exibidos aqui</span>
        </div>
      </div>
    </div>

    <div id="leads-tab" class="tab-content">
      <div class="leads-section">
        <div class="leads-section-header">
          <h3 class="leads-section-title">
            <i class="fas fa-user-plus"></i>
            Leads por Tipo
          </h3>
          <div class="lead-filters">
            <select class="form-control" id="lead-period-filter">
              <option value="7">Últimos 7 dias</option>
              <option value="30" selected>Últimos 30 dias</option>
              <option value="90">Últimos 90 dias</option>
            </select>
          </div>
        </div>
        
        <ul class="leads-detail-list" id="leads-by-type">
          <!-- Será preenchido dinamicamente -->
        </ul>
        
        <div class="leads-chart">
          <div class="chart-area">
            <h4 class="chart-title">
              <i class="fas fa-chart-pie"></i>
              Distribuição de Leads
            </h4>
            <div id="leads-pie-chart" class="chart-placeholder">
              <span>Gráfico de distribuição será exibido aqui</span>
            </div>
          </div>
          <div class="chart-area">
            <h4 class="chart-title">
              <i class="fas fa-chart-line"></i>
              Tendência de Leads
            </h4>
            <div id="leads-trend-chart" class="chart-placeholder">
              <span>Gráfico de tendência será exibido aqui</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="audience-tab" class="tab-content">
      <div class="filter-card">
        <h3 class="chart-title">
          <i class="fas fa-users"></i>
          Métricas de Audiência
        </h3>
        <div class="chart-placeholder">
          <span>Dados de audiência serão exibidos aqui</span>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Insira todo o código JavaScript aqui
    // Variável global para armazenar os dados dos últimos 90 dias
let dadosCompletos = [];
let comparisonChart = null;
let conversionTrendChart = null;

// Função para mostrar o spinner de carregamento
function showLoading
