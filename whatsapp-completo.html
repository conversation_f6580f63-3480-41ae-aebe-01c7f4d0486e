<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disparador WhatsApp - Sistema Completo</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://unpkg.com/papaparse@5.3.2/papaparse.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
        }
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            line-height: 1.3;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes slideDown {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .animate-fade-in { animation: fadeIn 0.3s ease-in-out; }
        .animate-slide-up { animation: slideUp 0.3s ease-out; }
        .animate-slide-down { animation: slideDown 0.3s ease-out; }
        ::-webkit-scrollbar { width: 6px; }
        ::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 3px; }
        ::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 3px; }
        ::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }
        .text-shadow { text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
    </style>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff', 100: '#e0f2fe', 200: '#bae6fd', 300: '#7dd3fc',
                            400: '#38bdf8', 500: '#0ea5e9', 600: '#0284c7', 700: '#0369a1',
                            800: '#075985', 900: '#0c4a6e',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        heading: ['Poppins', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useCallback, useMemo, createContext, useContext, useRef } = React;
        const { 
            LayoutDashboard, Send, Users, Server, Bookmark, Settings: SettingsIcon,
            MessageSquare, Upload, CheckCircle, AlertCircle, X, RefreshCw, PlusCircle,
            Trash2, Edit, QrCode, Power, Clock, FileText, Phone, List, Save,
            ArrowLeft, Activity, StopCircle, Play, Bold, Italic, Strikethrough,
            Code, Quote, Type, Sparkles, AtSign, Mic, Paperclip, Tag, Filter,
            Search, ChevronDown, Eye, MousePointer, Target
        } = lucide;

        // ==================== STORAGE UTILITIES ====================
        const STORAGE_KEYS = {
            SETTINGS: 'whatsappSenderSettings',
            TEMPLATES: 'whatsappSenderTemplates',
            STATS: 'whatsappSenderStats',
            CONTACTS: 'whatsappSenderContacts',
            GROUPS: 'whatsappSenderGroups',
            CAMPAIGN_PROGRESS: 'whatsappSenderCampaignProgress',
            ALL_TAGS: 'whatsappSenderAllTags',
        };

        const loadFromStorage = (key, defaultValue) => {
            try {
                const saved = localStorage.getItem(key);
                return saved ? JSON.parse(saved) : defaultValue;
            } catch (error) {
                console.error(`Failed to load ${key}`, error);
                return defaultValue;
            }
        };

        const saveToStorage = (key, value) => {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (error) {
                console.error(`Failed to save ${key}`, error);
            }
        };

        // ==================== CONTEXTS ====================
        const AppContext = createContext();
        const NotificationContext = createContext();

        // ==================== NOTIFICATION PROVIDER ====================
        const NotificationProvider = ({ children }) => {
            const [notifications, setNotifications] = useState([]);

            const addNotification = useCallback((message, type = 'info') => {
                const id = Date.now() + Math.random();
                setNotifications(current => [...current, { id, message, type }]);
            }, []);

            const removeNotification = useCallback((id) => {
                setNotifications(current => current.filter(n => n.id !== id));
            }, []);

            return React.createElement(NotificationContext.Provider, {
                value: { addNotification, removeNotification }
            }, children);
        };

        const useNotification = () => {
            const context = useContext(NotificationContext);
            if (!context) throw new Error('useNotification must be used within NotificationProvider');
            return context;
        };

        // ==================== APP PROVIDER ====================
        const AppProvider = ({ children }) => {
            const [currentView, setCurrentView] = useState('dashboard');
            const [settings, setSettings] = useState(() => loadFromStorage(STORAGE_KEYS.SETTINGS, {
                baseUrl: '', apiKey: '', minDelay: '2', maxDelay: '5',
                aiProvider: 'gemini', aiApiKey: '',
                systemPrompt: 'Você é um especialista em marketing para WhatsApp. Crie mensagens curtas, claras e persuasivas.'
            }));
            const [contacts, setContacts] = useState(() => loadFromStorage(STORAGE_KEYS.CONTACTS, []));
            const [groups, setGroups] = useState(() => loadFromStorage(STORAGE_KEYS.GROUPS, []));
            const [templates, setTemplates] = useState(() => loadFromStorage(STORAGE_KEYS.TEMPLATES, []));
            const [instances, setInstances] = useState([]);
            const [stats, setStats] = useState(() => loadFromStorage(STORAGE_KEYS.STATS, { total: 0, success: 0, errors: 0, lastRun: null }));
            const [campaignState, setCampaignState] = useState({
                isSending: false, progress: 0, success: 0, errors: [], current: 0, total: 0, message: '', config: null
            });

            const updateSettings = useCallback((newSettings) => {
                setSettings(newSettings);
                saveToStorage(STORAGE_KEYS.SETTINGS, newSettings);
            }, []);

            const updateContacts = useCallback((newContacts) => {
                setContacts(newContacts);
                saveToStorage(STORAGE_KEYS.CONTACTS, newContacts);
            }, []);

            const updateGroups = useCallback((newGroups) => {
                setGroups(newGroups);
                saveToStorage(STORAGE_KEYS.GROUPS, newGroups);
            }, []);

            const updateTemplates = useCallback((newTemplates) => {
                setTemplates(newTemplates);
                saveToStorage(STORAGE_KEYS.TEMPLATES, newTemplates);
            }, []);

            const value = {
                currentView, setCurrentView,
                settings, updateSettings,
                contacts, updateContacts,
                groups, updateGroups,
                templates, updateTemplates,
                instances, setInstances,
                stats, setStats,
                campaignState, setCampaignState
            };

            return React.createElement(AppContext.Provider, { value }, children);
        };

        const useApp = () => {
            const context = useContext(AppContext);
            if (!context) throw new Error('useApp must be used within AppProvider');
            return context;
        };

        // ==================== UI COMPONENTS ====================
        const Button = ({ children, variant = 'primary', size = 'md', loading = false, icon, fullWidth = false, className = '', ...props }) => {
            const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
            const variantClasses = {
                primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md',
                secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500',
                success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm hover:shadow-md',
                danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md',
                warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 shadow-sm hover:shadow-md',
                ghost: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100 focus:ring-gray-500',
            };
            const sizeClasses = {
                sm: 'px-3 py-1.5 text-sm gap-1.5',
                md: 'px-4 py-2 text-sm gap-2',
                lg: 'px-6 py-3 text-base gap-2',
            };
            const widthClass = fullWidth ? 'w-full' : '';
            const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${className}`;
            
            return React.createElement('button', {
                className: classes,
                disabled: loading || props.disabled,
                ...props
            }, 
                loading ? React.createElement('div', { className: 'animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full' }) : icon,
                children
            );
        };

        const Card = ({ children, className = '', padding = 'md' }) => {
            const paddingClasses = { sm: 'p-4', md: 'p-6', lg: 'p-8' };
            return React.createElement('div', {
                className: `bg-white border border-gray-200 rounded-xl shadow-sm ${paddingClasses[padding]} ${className}`
            }, children);
        };

        const Input = ({ label, error, helperText, fullWidth = false, className = '', ...props }) => {
            const baseClasses = 'border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 px-3 py-2';
            const errorClasses = error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : '';
            const widthClass = fullWidth ? 'w-full' : '';
            const inputClasses = `${baseClasses} ${errorClasses} ${widthClass} ${className}`;

            return React.createElement('div', { className: fullWidth ? 'w-full' : '' },
                label && React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, label),
                React.createElement('input', { className: inputClasses, ...props }),
                error && React.createElement('p', { className: 'mt-1 text-sm text-red-600' }, error),
                helperText && !error && React.createElement('p', { className: 'mt-1 text-sm text-gray-500' }, helperText)
            );
        };

        const Textarea = ({ label, error, helperText, fullWidth = false, className = '', ...props }) => {
            const baseClasses = 'border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 px-3 py-2';
            const errorClasses = error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : '';
            const widthClass = fullWidth ? 'w-full' : '';
            const textareaClasses = `${baseClasses} ${errorClasses} ${widthClass} ${className}`;

            return React.createElement('div', { className: fullWidth ? 'w-full' : '' },
                label && React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, label),
                React.createElement('textarea', { className: textareaClasses, ...props }),
                error && React.createElement('p', { className: 'mt-1 text-sm text-red-600' }, error),
                helperText && !error && React.createElement('p', { className: 'mt-1 text-sm text-gray-500' }, helperText)
            );
        };

        const Modal = ({ isOpen, onClose, title, children, size = 'md' }) => {
            if (!isOpen) return null;

            const sizeClasses = {
                sm: 'max-w-sm', md: 'max-w-md', lg: 'max-w-lg', xl: 'max-w-xl', '2xl': 'max-w-2xl'
            };

            return React.createElement('div', {
                className: 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 animate-fade-in',
                onClick: (e) => e.target === e.currentTarget && onClose()
            },
                React.createElement('div', { className: `bg-white rounded-xl shadow-2xl w-full ${sizeClasses[size]} animate-slide-up` },
                    React.createElement('div', { className: 'flex justify-between items-center p-6 border-b border-gray-200' },
                        React.createElement('h3', { className: 'text-lg font-semibold text-gray-900' }, title),
                        React.createElement(Button, {
                            variant: 'ghost',
                            size: 'sm',
                            onClick: onClose,
                            icon: React.createElement(X, { size: 20 }),
                            className: 'text-gray-400 hover:text-gray-600 p-1'
                        })
                    ),
                    React.createElement('div', { className: 'p-6 max-h-[70vh] overflow-y-auto' }, children)
                )
            );
        };

        const Toast = ({ notification, onClose }) => {
            const { id, message, type } = notification;

            useEffect(() => {
                const timer = setTimeout(() => onClose(id), 5000);
                return () => clearTimeout(timer);
            }, [id, onClose]);

            const styles = {
                success: { bg: 'bg-green-500', icon: React.createElement(CheckCircle, { className: 'h-6 w-6' }) },
                error: { bg: 'bg-red-500', icon: React.createElement(AlertCircle, { className: 'h-6 w-6' }) },
                info: { bg: 'bg-blue-500', icon: React.createElement(AlertCircle, { className: 'h-6 w-6' }) },
            };

            const currentStyle = styles[type] || styles.info;

            return React.createElement('div', {
                className: `relative flex items-center gap-4 w-full p-4 text-white rounded-lg shadow-lg animate-slide-down ${currentStyle.bg}`
            },
                React.createElement('div', {}, currentStyle.icon),
                React.createElement('p', { className: 'flex-1 text-sm font-medium whitespace-pre-wrap' }, message),
                React.createElement('button', {
                    onClick: () => onClose(id),
                    className: 'p-1 rounded-full hover:bg-white/20 transition-colors'
                }, React.createElement(X, { size: 18 }))
            );
        };

        const ToastContainer = ({ notifications, removeNotification }) => {
            return React.createElement('div', {
                className: 'fixed top-5 right-5 z-[100] w-full max-w-sm space-y-3'
            },
                notifications.map(notification =>
                    React.createElement(Toast, {
                        key: notification.id,
                        notification,
                        onClose: removeNotification
                    })
                )
            );
        };

        // ==================== HEADER COMPONENT ====================
        const Header = () => {
            const { currentView, setCurrentView } = useApp();

            const NavButton = ({ targetView, icon: Icon, label }) => {
                const isActive = currentView === targetView;
                
                return React.createElement('button', {
                    onClick: () => setCurrentView(targetView),
                    className: `flex-1 md:flex-initial flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                        isActive 
                            ? 'bg-white text-blue-700 shadow-sm' 
                            : 'text-gray-500 hover:bg-gray-200 hover:text-gray-700'
                    }`
                },
                    React.createElement(Icon, { size: 18 }),
                    React.createElement('span', {}, label)
                );
            };

            return React.createElement('header', {
                className: 'flex flex-col md:flex-row items-center justify-between mb-6 gap-4'
            },
                React.createElement('h1', {
                    className: 'text-3xl font-bold text-gray-900 font-heading text-shadow'
                }, 'Disparador em Massa'),
                
                React.createElement('nav', { className: 'w-full md:w-auto' },
                    React.createElement('div', {
                        className: 'flex items-center justify-center gap-1 p-1 bg-gray-100 rounded-xl flex-wrap shadow-sm'
                    },
                        React.createElement(NavButton, { targetView: 'dashboard', icon: LayoutDashboard, label: 'Dashboard' }),
                        React.createElement(NavButton, { targetView: 'main', icon: Send, label: 'Disparador' }),
                        React.createElement(NavButton, { targetView: 'contacts', icon: Users, label: 'Contatos' }),
                        React.createElement(NavButton, { targetView: 'groups', icon: Users, label: 'Grupos' }),
                        React.createElement(NavButton, { targetView: 'instances', icon: Server, label: 'Instâncias' }),
                        React.createElement(NavButton, { targetView: 'templates', icon: Bookmark, label: 'Templates' }),
                        React.createElement('button', {
                            onClick: () => setCurrentView('settings'),
                            className: `p-2.5 rounded-lg transition-all duration-200 ${
                                currentView === 'settings' 
                                    ? 'bg-white text-blue-600 shadow-sm' 
                                    : 'text-gray-500 hover:bg-gray-200 hover:text-gray-700'
                            }`
                        }, React.createElement(SettingsIcon, { size: 18 }))
                    )
                )
            );
        };

        // ==================== DASHBOARD VIEW ====================
