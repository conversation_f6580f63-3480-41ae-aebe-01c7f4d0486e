-- Create enums
CREATE TYPE user_type AS ENUM ('admin_global', 'admin_empresa', 'funcionario');
CREATE TYPE property_type AS ENUM ('casa', 'apartamento', 'terreno', 'comercial', 'rural');
CREATE TYPE property_status AS ENUM ('disponivel', 'vendido', 'alugado', 'reservado');
CREATE TYPE lead_status AS ENUM ('novo', 'em_atendimento', 'concluido', 'cancelado');

-- Create tables
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nome TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  senha TEXT NOT NULL,
  tipo_usuario user_type NOT NULL,
  cliente_ativo BOOLEAN DEFAULT TRUE,
  onboarding_feito BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Other tables follow similar pattern