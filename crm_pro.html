<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM WhatsApp Style Pro - Interações com IA</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00a884;
            --primary-dark: #008069;
            --secondary-color: #25d366;
            --accent-color: #128c7e;
            --bg-primary: #f0f2f5;
            --bg-secondary: #ffffff;
            --bg-chat: #efeae2;
            --text-primary: #111b21;
            --text-secondary: #667781;
            --text-muted: #8696a0;
            --border-color: #e9edef;
            --shadow-light: 0 1px 3px rgba(0,0,0,0.12);
            --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.12);
            --gradient-primary: linear-gradient(135deg, #00a884, #25d366);
            --gradient-secondary: linear-gradient(135deg, #667eea, #764ba2);
            --gradient-success: linear-gradient(135deg, #4caf50, #8bc34a);
            --gradient-warning: linear-gradient(135deg, #ff9800, #ffc107);
            --gradient-danger: linear-gradient(135deg, #f44336, #e91e63);
        }

        [data-theme="dark"] {
            --bg-primary: #111b21;
            --bg-secondary: #202c33;
            --bg-chat: #0b141a;
            --text-primary: #e9edef;
            --text-secondary: #aebac1;
            --text-muted: #8696a0;
            --border-color: #2a3942;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-primary);
            height: 100vh;
            overflow: hidden;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .app-container {
            display: flex;
            height: 100vh;
            background: var(--bg-primary);
            position: relative;
        }

        .optimized-scroll {
            will-change: scroll-position;
            transform: translateZ(0);
        }

        .gpu-accelerated {
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000px;
        }

        /* SIDEBAR */
        .sidebar {
            width: 420px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            position: relative;
            box-shadow: var(--shadow-medium);
            z-index: 10;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            background: var(--gradient-primary);
            color: white;
            padding: 24px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .sidebar-title i {
            font-size: 24px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .sidebar-actions {
            display: flex;
            gap: 8px;
            position: relative;
            z-index: 1;
        }

        .action-btn {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .action-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .action-btn i {
            position: relative;
            z-index: 1;
        }

        /* Stats Bar */
        .stats-bar {
            background: linear-gradient(90deg, var(--bg-primary), var(--border-color));
            padding: 12px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-around;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            transition: all 0.3s ease;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
        }

        .stat-item:hover {
            background: rgba(0,168,132,0.1);
            color: var(--primary-color);
        }

        .stat-value {
            font-size: 16px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .stat-label {
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Filtros */
        .filter-section {
            background: var(--bg-primary);
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .search-container {
            position: relative;
            margin-bottom: 12px;
        }

        .search-input {
            width: 100%;
            padding: 14px 50px 14px 20px;
            border: 2px solid transparent;
            border-radius: 25px;
            background: var(--bg-secondary);
            font-size: 14px;
            outline: none;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            font-family: inherit;
            color: var(--text-primary);
        }

        .search-input::placeholder {
            color: var(--text-muted);
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0,168,132,0.1);
            transform: translateY(-1px);
        }

        .search-icon {
            position: absolute;
            right: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            transition: all 0.3s ease;
        }

        .search-input:focus + .search-icon {
            color: var(--primary-color);
            transform: translateY(-50%) scale(1.1);
        }

        .filter-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .filter-toggle {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filter-toggle:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .filter-count {
            background: rgba(255,255,255,0.2);
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            min-width: 18px;
            text-align: center;
        }

        .advanced-filters {
            background: var(--bg-secondary);
            margin-top: 12px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: var(--shadow-light);
            display: none;
            border: 1px solid var(--border-color);
        }

        .advanced-filters.active {
            display: block;
            animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
                max-height: 0;
            }
            to {
                opacity: 1;
                transform: translateY(0);
                max-height: 300px;
            }
        }

        .filter-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }

        .filter-field {
            position: relative;
        }

        .filter-field label {
            display: block;
            margin-bottom: 6px;
            font-size: 12px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filter-field select,
        .filter-field input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 13px;
            background: var(--bg-secondary);
            transition: all 0.3s ease;
            font-family: inherit;
            color: var(--text-primary);
        }

        .filter-field select:focus,
        .filter-field input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0,168,132,0.1);
        }

        .filter-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            margin-top: 16px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-secondary {
            background: var(--bg-primary);
            color: var(--text-secondary);
            border: 2px solid var(--border-color);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* Lista de Conversas */
        .conversations-list {
            flex: 1;
            overflow-y: auto;
            background: var(--bg-secondary);
            position: relative;
            contain: layout style paint;
        }

        .conversation-item {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            background: var(--bg-secondary);
            will-change: transform;
        }

        .conversation-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 0;
            background: var(--gradient-primary);
            transition: width 0.3s ease;
        }

        .conversation-item:hover {
            background: var(--bg-primary);
            transform: translateX(4px);
        }

        .conversation-item:hover::before {
            width: 4px;
        }

        .conversation-item.active {
            background: linear-gradient(90deg, rgba(0,168,132,0.1), rgba(0,168,132,0.05));
            border-right: 4px solid var(--primary-color);
            transform: translateX(0);
        }

        .conversation-item.active::before {
            width: 4px;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-name {
            font-weight: 700;
            font-size: 16px;
            color: var(--text-primary);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .conversation-avatar {
            width: 12px;
            height: 12px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: inline-block;
            position: relative;
        }

        .conversation-avatar::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 2px solid rgba(0,168,132,0.3);
            border-radius: 50%;
            animation: ping 2s infinite;
        }

        @keyframes ping {
            0% { transform: scale(1); opacity: 1; }
            75%, 100% { transform: scale(1.5); opacity: 0; }
        }

        .conversation-phone {
            font-size: 12px;
            color: var(--text-muted);
            font-family: 'Courier New', monospace;
        }

        .conversation-meta {
            text-align: right;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 6px;
        }

        .conversation-time {
            font-size: 11px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .conversation-count {
            background: var(--gradient-primary);
            color: white;
            border-radius: 12px;
            padding: 4px 8px;
            font-size: 10px;
            min-width: 20px;
            text-align: center;
            font-weight: 700;
            box-shadow: var(--shadow-light);
        }

        .conversation-preview {
            font-size: 13px;
            color: var(--text-secondary);
            margin: 8px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.4;
        }

        .conversation-tags {
            display: flex;
            gap: 6px;
            margin-top: 8px;
            flex-wrap: wrap;
        }

        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            transition: all 0.3s ease;
        }

        .tag:hover {
            transform: scale(1.05);
        }

        .tag.quality-boa { 
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9); 
            color: #2e7d32; 
        }
        .tag.quality-regular { 
            background: linear-gradient(135deg, #fff3e0, #ffe0b2); 
            color: #f57c00; 
        }
        .tag.quality-ruim { 
            background: linear-gradient(135deg, #ffebee, #ffcdd2); 
            color: #d32f2f; 
        }
        .tag.pendente { 
            background: linear-gradient(135deg, #f3e5f5, #e1bee7); 
            color: #7b1fa2; 
        }

        /* MAIN CHAT AREA */
        .main-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-chat);
            position: relative;
        }

        .chat-header {
            background: var(--bg-secondary);
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-light);
            position: relative;
        }

        .chat-header-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .chat-avatar {
            width: 48px;
            height: 48px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 18px;
            position: relative;
            box-shadow: var(--shadow-medium);
        }

        .chat-avatar::after {
            content: '';
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #4caf50;
            border: 2px solid white;
            border-radius: 50%;
        }

        .chat-details h3 {
            font-size: 18px;
            color: var(--text-primary);
            margin-bottom: 4px;
            font-weight: 700;
        }

        .chat-details span {
            font-size: 13px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #4caf50;
            border-radius: 50%;
            animation: blink 2s infinite;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .chat-actions {
            display: flex;
            gap: 8px;
        }

        .chat-action-btn {
            background: rgba(0,168,132,0.1);
            border: none;
            color: var(--primary-color);
            width: 44px;
            height: 44px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .chat-action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: var(--primary-color);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
            opacity: 0.1;
        }

        .chat-action-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .chat-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* Área de Mensagens */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            position: relative;
            contain: layout style paint;
        }

        .messages-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 49%, rgba(255,255,255,0.03) 50%, transparent 51%);
            pointer-events: none;
        }

        .message-group {
            margin-bottom: 24px;
            position: relative;
        }

        .message-date {
            text-align: center;
            margin: 32px 0;
            position: relative;
        }

        .date-badge {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            color: var(--text-secondary);
            box-shadow: var(--shadow-light);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            display: inline-block;
        }

        .date-badge::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -50px;
            right: calc(100% + 16px);
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--border-color), transparent);
        }

        .date-badge::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -50px;
            left: calc(100% + 16px);
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--border-color), transparent);
        }

        .message {
            margin-bottom: 12px;
            display: flex;
            align-items: flex-end;
            gap: 12px;
            animation: messageSlide 0.4s ease-out;
            will-change: transform;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.client {
            justify-content: flex-end;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .message-bubble:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }

        .message.client .message-bubble {
            background: linear-gradient(135deg, #dcf8c6, #d4edda);
            border-bottom-right-radius: 4px;
            border: 1px solid rgba(0,168,132,0.2);
        }

        .message.ia .message-bubble {
            background: var(--bg-secondary);
            border-bottom-left-radius: 4px;
            border: 1px solid var(--border-color);
            cursor: pointer;
        }

        .message.ia .message-bubble:hover {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
        }

        .message.ia .message-bubble.selected {
            border: 2px solid var(--primary-color);
            box-shadow: 0 0 10px rgba(0,168,132,0.3);
        }

        .message-content {
            font-size: 14px;
            line-height: 1.5;
            color: var(--text-primary);
            margin-bottom: 6px;
            word-break: break-word;
        }

        .message-content strong {
            font-weight: 700;
        }

        .message-content em {
            font-style: italic;
        }

        .message-content code {
            background: rgba(0,0,0,0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .message-content pre {
            background: rgba(0,0,0,0.1);
            padding: 8px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 4px 0;
        }

        .message-time {
            font-size: 11px;
            color: var(--text-muted);
            text-align: right;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 4px;
        }

        .message-sender {
            font-size: 11px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .message-status {
            color: var(--primary-color);
            font-size: 10px;
        }

        /* Painel de Validação */
        .validation-panel {
            background: var(--bg-secondary);
            border-left: 1px solid var(--border-color);
            width: 380px;
            display: flex;
            flex-direction: column;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-heavy);
            position: relative;
        }

        .validation-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--gradient-primary);
        }

        .validation-panel.hidden {
            transform: translateX(100%);
            box-shadow: none;
        }

        .validation-header {
            background: linear-gradient(135deg, var(--bg-primary), var(--border-color));
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .validation-title {
            font-size: 16px;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .validation-title i {
            color: var(--primary-color);
            font-size: 18px;
        }

        .close-validation {
            background: rgba(0,0,0,0.1);
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-validation:hover {
            background: rgba(244,67,54,0.1);
            color: #f44336;
            transform: rotate(90deg);
        }

        .validation-content {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            contain: layout style paint;
        }

        .validation-section {
            background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .validation-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
        }

        .validation-section h4 {
            color: var(--text-primary);
            margin-bottom: 16px;
            font-size: 14px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .validation-section h4 i {
            color: var(--primary-color);
            font-size: 16px;
        }

        .validation-grid {
            display: grid;
            gap: 16px;
        }

        .validation-field label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .validation-field select,
        .validation-field input,
        .validation-field textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--bg-secondary);
            transition: all 0.3s ease;
            font-family: inherit;
            color: var(--text-primary);
        }

        .validation-field select:focus,
        .validation-field input:focus,
        .validation-field textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0,168,132,0.1);
            transform: translateY(-1px);
        }

        .validation-field textarea {
            resize: vertical;
            min-height: 100px;
            line-height: 1.5;
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 12px;
            min-height: 40px;
            padding: 8px;
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .tags-container:hover {
            border-color: var(--primary-color);
            background: rgba(0,168,132,0.02);
        }

        .tag-input {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .tag-input input {
            flex: 1;
            padding: 10px 12px;
            border: 2px solid var(--border-color);
            border-radius: 20px;
            font-size: 12px;
            transition: all 0.3s ease;
            color: var(--text-primary);
            background: var(--bg-secondary);
        }

        .tag-input input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0,168,132,0.1);
        }

        .add-tag-btn {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .add-tag-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .tag-item {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            transition: all 0.3s ease;
            border: 1px solid rgba(25,118,210,0.2);
        }

        .tag-item:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-light);
        }

        .remove-tag {
            background: none;
            border: none;
            color: #1976d2;
            cursor: pointer;
            font-size: 12px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .remove-tag:hover {
            background: rgba(244,67,54,0.1);
            color: #f44336;
            transform: rotate(90deg);
        }

        .save-btn {
            background: var(--gradient-success);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 700;
            margin-top: 20px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .save-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .save-btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .save-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(76,175,80,0.3);
        }

        .save-btn i {
            margin-right: 8px;
        }

        /* Estados vazios */
        .empty-state {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--text-muted);
            text-align: center;
            padding: 60px 40px;
        }

        .empty-state i {
            font-size: 80px;
            margin-bottom: 24px;
            opacity: 0.3;
            background: var(--gradient-secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .empty-state h3 {
            margin-bottom: 12px;
            color: var(--text-primary);
            font-size: 20px;
            font-weight: 700;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.5;
            max-width: 300px;
            color: var(--text-secondary);
        }

        /* Notificações */
        .notification {
            position: fixed;
            top: 24px;
            right: 24px;
            padding: 16px 24px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-heavy);
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 300px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--gradient-success);
        }

        .notification.error {
            background: var(--gradient-danger);
        }

        .notification.warning {
            background: var(--gradient-warning);
        }

        .notification.info {
            background: var(--gradient-secondary);
        }

        .notification i {
            font-size: 18px;
        }

        /* Scrollbars */
        .conversations-list::-webkit-scrollbar,
        .messages-container::-webkit-scrollbar,
        .validation-content::-webkit-scrollbar {
            width: 8px;
        }

        .conversations-list::-webkit-scrollbar-track,
        .messages-container::-webkit-scrollbar-track,
        .validation-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .conversations-list::-webkit-scrollbar-thumb,
        .messages-container::-webkit-scrollbar-thumb,
        .validation-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(0,168,132,0.3), rgba(0,168,132,0.6));
            border-radius: 10px;
            border: 2px solid transparent;
            background-clip: content-box;
        }

        .conversations-list::-webkit-scrollbar-thumb:hover,
        .messages-container::-webkit-scrollbar-thumb:hover,
        .validation-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(0,168,132,0.6), rgba(0,168,132,0.8));
        }

        /* Loading States */
        .loading-spinner {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid rgba(0,168,132,0.3);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .skeleton {
            background: linear-gradient(90deg, var(--border-color) 25%, var(--bg-primary) 50%, var(--border-color) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Responsividade */
        @media (max-width: 1024px) {
            .sidebar {
                width: 350px;
            }
            
            .validation-panel {
                width: 320px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: absolute;
                z-index: 100;
                transform: translateX(-100%);
                transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .validation-panel {
                width: 100%;
                position: absolute;
                z-index: 200;
            }

            .main-chat {
                width: 100%;
            }

            .filter-row {
                grid-template-columns: 1fr;
            }

            .message-bubble {
                max-width: 85%;
            }
        }

        /* Animações extras */
        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .bounce-in {
            animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        .glow-on-hover {
            transition: all 0.3s ease;
        }

        .glow-on-hover:hover {
            box-shadow: 0 0 20px rgba(0,168,132,0.4);
        }

        /* Indicadores de status */
        .status-online { color: #4caf50; }
        .status-away { color: #ff9800; }
        .status-busy { color: #f44336; }
        .status-offline { color: #9e9e9e; }

        /* Virtual scrolling */
        .virtual-list {
            height: 100%;
            overflow: auto;
        }

        .virtual-item {
            will-change: transform;
            contain: layout style paint;
        }

        /* Estilos adicionais */
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 12px;
        }

        .analysis-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: rgba(0,168,132,0.1);
            border-radius: 6px;
            font-size: 12px;
            color: var(--text-primary);
        }

        .suggested-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin: 8px 0;
        }

        .suggested-tag {
            background: rgba(0,168,132,0.1);
            color: var(--primary-color);
            border: 1px solid rgba(0,168,132,0.3);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggested-tag:hover {
            background: var(--primary-color);
            color: white;
        }

        .score-display {
            text-align: center;
            font-weight: 700;
            color: var(--primary-color);
            margin-top: 6px;
        }

        .validation-actions {
            display: flex;
            gap: 12px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .word-count {
            font-size: 11px;
            color: var(--text-muted);
            margin-top: 4px;
            text-align: right;
        }

        .temperature-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .temperature-bar {
            width: 60px;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
        }

        .temperature-fill {
            height: 100%;
            background: var(--gradient-primary);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body data-theme="light">
    <div class="app-container">
        <!-- SIDEBAR -->
        <aside class="sidebar" id="sidebar">
            <header class="sidebar-header">
                <div class="sidebar-title">
                    <i class="fas fa-comments"></i>
                    CRM Pro
                </div>
                <div class="sidebar-actions">
                    <button class="action-btn glow-on-hover" onclick="refreshConversations()" title="Atualizar">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="action-btn glow-on-hover" onclick="toggleFilters()" title="Filtros">
                        <i class="fas fa-filter"></i>
                    </button>
                    <button class="action-btn glow-on-hover" onclick="exportAllData()" title="Exportar Tudo">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn glow-on-hover" onclick="toggleTheme()" title="Alternar Tema">
                        <i class="fas fa-moon" id="theme-icon"></i>
                    </button>
                    <button class="action-btn glow-on-hover" onclick="showSettings()" title="Configurações">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </header>

            <section class="stats-bar">
                <div class="stat-item" onclick="filterByStatus('all')">
                    <div class="stat-value" id="total-conversations">0</div>
                    <div class="stat-label">Conversas</div>
                </div>
                <div class="stat-item" onclick="filterByStatus('pending')">
                    <div class="stat-value" id="pending-validations">0</div>
                    <div class="stat-label">Pendentes</div>
                </div>
                <div class="stat-item" onclick="filterByStatus('validated')">
                    <div class="stat-value" id="validated-count">0</div>
                    <div class="stat-label">Validadas</div>
                </div>
                <div class="stat-item" onclick="showAnalytics()">
                    <div class="stat-value" id="avg-score">0%</div>
                    <div class="stat-label">Qualidade</div>
                </div>
            </section>

            <section class="filter-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Buscar conversas, clientes, conteúdo..." id="search-input">
                    <i class="fas fa-search search-icon"></i>
                </div>
                
                <div class="filter-controls">
                    <button class="filter-toggle" onclick="toggleAdvancedFilters()">
                        <i class="fas fa-sliders-h"></i>
                        Filtros
                        <span class="filter-count" id="filter-count">0</span>
                    </button>
                    <button class="btn btn-secondary" onclick="clearAllFilters()">
                        <i class="fas fa-times"></i>
                        Limpar
                    </button>
                </div>

                <div class="advanced-filters" id="advanced-filters">
                    <div class="filter-row">
                        <div class="filter-field">
                            <label for="filter-quality">Qualidade</label>
                            <select id="filter-quality">
                                <option value="">Todas</option>
                                <option value="boa">✅ Boa</option>
                                <option value="regular">⚠️ Regular</option>
                                <option value="ruim">❌ Ruim</option>
                                <option value="pendente">⏳ Pendente</option>
                            </select>
                        </div>
                        <div class="filter-field">
                            <label for="filter-date">Data</label>
                            <input type="date" id="filter-date">
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-field">
                            <label for="filter-period">Período</label>
                            <select id="filter-period">
                                <option value="">Todos</option>
                                <option value="today">Hoje</option>
                                <option value="week">Esta semana</option>
                                <option value="month">Este mês</option>
                            </select>
                        </div>
                        <div class="filter-field">
                            <label for="filter-tag">Tags</label>
                            <input type="text" id="filter-tag" placeholder="Buscar por tag">
                        </div>
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-secondary" onclick="saveFilterPreset()">
                            <i class="fas fa-save"></i>
                            Salvar Filtro
                        </button>
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-search"></i>
                            Aplicar
                        </button>
                    </div>
                </div>
            </section>

            <div class="conversations-list optimized-scroll" id="conversations-list">
                <div class="empty-state">
                    <div class="loading-spinner"></div>
                    <h3>Carregando conversas...</h3>
                    <p>Aguarde enquanto buscamos suas interações</p>
                </div>
            </div>
        </aside>

        <!-- MAIN CHAT AREA -->
        <main class="main-chat">
            <header class="chat-header" id="chat-header" style="display: none;">
                <div class="chat-header-info">
                    <div class="chat-avatar" id="chat-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="chat-details">
                        <h3 id="chat-name">Nome do Cliente</h3>
                        <span id="chat-phone">
                            <div class="status-indicator"></div>
                            +55 11 99999-9999
                        </span>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="chat-action-btn glow-on-hover" onclick="toggleValidationPanel()" title="Painel de Validações">
                        <i class="fas fa-clipboard-check"></i>
                    </button>
                    <button class="chat-action-btn glow-on-hover" onclick="exportConversation()" title="Exportar Conversa">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="chat-action-btn glow-on-hover" onclick="shareConversation()" title="Compartilhar">
                        <i class="fas fa-share-alt"></i>
                    </button>
                    <button class="chat-action-btn glow-on-hover" onclick="printConversation()" title="Imprimir">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="chat-action-btn glow-on-hover" onclick="showConversationAnalytics()" title="Analytics">
                        <i class="fas fa-chart-line"></i>
                    </button>
                    <button class="chat-action-btn glow-on-hover" onclick="toggleFullscreen()" title="Tela Cheia">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </header>

            <div class="messages-container optimized-scroll" id="messages-container">
                <div class="empty-state">
                    <i class="fas fa-comments"></i>
                    <h3>Selecione uma conversa</h3>
                    <p>Escolha uma conversa da lista para visualizar as mensagens e interações com a IA</p>
                </div>
            </div>
        </main>

        <!-- PAINEL DE VALIDAÇÃO -->
        <aside class="validation-panel hidden" id="validation-panel">
            <header class="validation-header">
                <div class="validation-title">
                    <i class="fas fa-clipboard-check"></i>
                    Painel de Validação
                </div>
                <button class="close-validation" onclick="toggleValidationPanel()">
                    <i class="fas fa-times"></i>
                </button>
            </header>

            <div class="validation-content optimized-scroll" id="validation-content">
                <div class="empty-state">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>Selecione uma mensagem</h3>
                    <p>Clique em uma mensagem da IA para iniciar a validação e adicionar feedback</p>
                </div>
            </div>
        </aside>
    </div>

    <div id="notification-container"></div>

    <script>
        // Configurações da API - MOVIDAS PARA VARIÁVEIS DE AMBIENTE EM PRODUÇÃO
        const API_CONFIG = {
            SUPABASE_URL: 'https://lkovjzghzguasnrnshoa.supabase.co',
            SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxrb3Zqemdoemd1YXNucm5zaG9hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjUwNzUwMjYsImV4cCI6MjA0MDY1MTAyNn0.10LWgS74tMJvNVrvRwgBKPFoNSdv0Lo4QZj5TGSNREg'
        };

        // Estado global da aplicação
        class AppStateManager {
            constructor() {
                this.state = {
                    allConversations: [],
                    filteredConversations: [],
                    currentConversation: null,
                    selectedMessage: null,
                    conversationTags: {},
                    filterPresets: [],
                    theme: 'light',
                    isLoading: false,
                    stats: {
                        total: 0,
                        pending: 0,
                        validated: 0,
                        avgQuality: 0
                    },
                    cache: new Map(),
                    virtualScrolling: {
                        itemHeight: 120, // Ajuste conforme o CSS
                        visibleItems: 10, // Calculado dinamicamente
                        scrollTop: 0
                    }
                };
                this.listeners = [];
            }
            
            setState(newState) {
                this.state = { ...this.state, ...newState };
                this.notifyListeners();
            }
            
            notifyListeners() {
                this.listeners.forEach(listener => listener(this.state));
            }
            
            addListener(listener) {
                this.listeners.push(listener);
            }
        }

        const appState = new AppStateManager();

        // Cache para otimização
        const cache = {
            conversations: new Map(),
            renderedConversationItems: new Map(), // Cache para HTML de itens de conversa
            renderedMessages: new Map(), // Cache para HTML de mensagens
            timers: new Map()
        };

        // Inicialização
        document.addEventListener('DOMContentLoaded', async () => {
            showNotification('Inicializando CRM...', 'info');
            await initializeApp();
        });

        async function initializeApp() {
            try {
                appState.setState({ isLoading: true });
                updateLoadingState(true);
                
                loadSavedData();
                setupEventListeners();
                setupPerformanceOptimizations();
                
                await loadConversationsOptimized();
                
                showNotification('CRM carregado com sucesso!', 'success');
            } catch (error) {
                console.error('Erro na inicialização:', error);
                showNotification('Erro ao carregar o CRM', 'error');
            } finally {
                appState.setState({ isLoading: false });
                updateLoadingState(false);
            }
        }

        function setupPerformanceOptimizations() {
            // Intersection Observer para lazy loading (se houver elementos com data-lazy)
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && entry.target.dataset.lazy) {
                        loadLazyContent(entry.target); // Implementar loadLazyContent se necessário
                    }
                });
            });

            document.querySelectorAll('[data-lazy]').forEach(el => observer.observe(el));

            // Otimizar scroll handlers
            const scrollContainers = document.querySelectorAll('.optimized-scroll');
            scrollContainers.forEach(container => {
                container.addEventListener('scroll', throttle(handleScroll, 16), { passive: true });
            });
        }

        function setupEventListeners() {
            document.getElementById('search-input').addEventListener('input', 
                debounce(handleSearchOptimized, 250)); // Aumentar debounce para digitação
            
            const filterElements = ['filter-quality', 'filter-date', 'filter-period', 'filter-tag'];
            filterElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', debounce(applyFiltersOptimized, 200));
                }
            });

            document.addEventListener('keydown', handleKeyboardShortcuts);
            setInterval(autoSave, 60000); // Aumentar intervalo do autoSave

            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', handleSystemThemeChange); // Usar addEventListener
        }

        function handleKeyboardShortcuts(event) {
            if (event.ctrlKey || event.metaKey) {
                switch(event.key.toLowerCase()) { // Normalizar para minúsculas
                    case 'f':
                        event.preventDefault();
                        document.getElementById('search-input').focus();
                        break;
                    case 's':
                        event.preventDefault();
                        if (appState.state.selectedMessage) {
                            document.querySelector('.save-btn')?.click(); // Simular clique no botão
                        }
                        break;
                    case 'e':
                        event.preventDefault();
                        if (appState.state.currentConversation) {
                            exportConversation();
                        }
                        break;
                    case 'd': // Para alternar tema
                        event.preventDefault();
                        toggleTheme();
                        break;
                }
            }
        }
              // Carregar conversas otimizado
        async function loadConversationsOptimized() {
            try {
                const cacheKey = 'conversations_data'; // Chave mais genérica para dados
                if (cache.conversations.has(cacheKey)) {
                    const cached = cache.conversations.get(cacheKey);
                    if (Date.now() - (cached.timestamp || 0) < 300000) { // Cache de 5 minutos
                        appState.setState({
                            allConversations: cached.data,
                            filteredConversations: [...cached.data]
                        });
                        renderConversationsListOptimized();
                        updateStats();
                        return;
                    }
                }

                showSkeletonLoading('conversations-list', 5, renderConversationSkeleton);

                const response = await fetch(`${API_CONFIG.SUPABASE_URL}/rest/v1/maqpeças_interacoes?select=*&order=data.desc,hora.desc`, {
                    headers: {
                        'apikey': API_CONFIG.SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${API_CONFIG.SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) throw new Error(`Erro ao carregar dados: ${response.statusText}`);

                const data = await response.json();
                
                const processedData = await processDataInChunks(data);
                
                appState.setState({
                    allConversations: processedData,
                    filteredConversations: [...processedData]
                });
                
                cache.conversations.set(cacheKey, { data: processedData, timestamp: Date.now() });
                
                renderConversationsListOptimized();
                updateStats();
                
            } catch (error) {
                console.error('Erro ao carregar conversas:', error);
                showNotification('Erro ao carregar conversas', 'error');
                document.getElementById('conversations-list').innerHTML = `
                    <div class="empty-state fade-in">
                        <i class="fas fa-wifi-slash"></i>
                        <h3>Erro de Conexão</h3>
                        <p>Não foi possível carregar as conversas. Verifique sua internet.</p>
                    </div>`;
            }
        }

        async function processDataInChunks(data, chunkSize = 50) {
            const processedData = [];
            for (let i = 0; i < data.length; i += chunkSize) {
                const chunk = data.slice(i, i + chunkSize);
                const processedChunk = chunk.map(item => ({
                    ...item,
                    pergunta_cliente_formatted: formatWhatsAppText(item.pergunta_cliente),
                    reposta_ia_formatted: formatWhatsAppText(item.reposta_ia),
                    wordCount: countWords(item.reposta_ia),
                    sentiment: analyzeSentiment(item.reposta_ia || ''),
                    temperature: calculateTemperature(item.reposta_ia || '')
                }));
                processedData.push(...processedChunk);
                await new Promise(resolve => setTimeout(resolve, 0)); // Yield to main thread
            }
            return processedData;
        }
        
        const WHATSAPP_FORMATTING_RULES = [
            { regex: /\*([^*]+)\*/g, replacement: '<strong>$1</strong>' },
            { regex: /_([^_]+)_/g, replacement: '<em>$1</em>' },
            { regex: /```([\s\S]*?)```/g, replacement: '<pre>$1</pre>' },
            { regex: /`([^`]+)`/g, replacement: '<code>$1</code>' },
            { regex: /~([^~]+)~/g, replacement: '<del>$1</del>' },
            { regex: /(https?:\/\/[^\s]+)/g, replacement: '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>' } // Adicionado noreferrer
        ];

        function formatWhatsAppText(text) {
            if (!text) return '';
            let formattedText = text;
            WHATSAPP_FORMATTING_RULES.forEach(rule => {
                formattedText = formattedText.replace(rule.regex, rule.replacement);
            });
            return formattedText.replace(/\n/g, '<br>');
        }

        function countWords(text) {
            if (!text) return 0;
            return text.trim().split(/\s+/).filter(Boolean).length;
        }

        function calculateTemperature(text) {
            if (!text) return 0;
            const len = text.length;
            const sentences = (text.match(/[.!?]+/g) || []).length + 1;
            const technicalWords = (text.match(/\b(API|SQL|JSON|HTTP|URL|ID|UUID)\b/gi) || []).length;
            const questions = (text.match(/\?/g) || []).length;
            const links = (text.match(/https?:\/\/[^\s]+/g) || []).length;

            const factors = {
                length: Math.min(len / 1000, 1) * 25,
                complexity: Math.min(sentences / (len / 100 || 1), 1) * 20, // Sentences per 100 chars
                technical: Math.min(technicalWords / 5, 1) * 20,
                questions: Math.min(questions / 3, 1) * 15,
                links: Math.min(links / 2, 1) * 20
            };
            
            return Math.min(Math.round(Object.values(factors).reduce((a, b) => a + b, 0)), 100);
        }

        function renderConversationsListOptimized() {
            const container = document.getElementById('conversations-list');
            const groupedConversations = groupConversationsByPhoneOptimized(appState.state.filteredConversations);

            if (groupedConversations.length === 0) {
                container.innerHTML = `
                    <div class="empty-state fade-in">
                        <i class="fas fa-search"></i>
                        <h3>Nenhuma conversa encontrada</h3>
                        <p>Tente ajustar os filtros de busca ou verificar a conexão</p>
                    </div>
                `;
                return;
            }
            
            // Ajustar itemHeight com base no CSS real se necessário
            appState.state.virtualScrolling.itemHeight = 120; // Exemplo, pode precisar de ajuste
            appState.state.virtualScrolling.visibleItems = Math.ceil(container.clientHeight / appState.state.virtualScrolling.itemHeight) + 2;


            if (groupedConversations.length > 30) { // Limiar para virtual scrolling
                renderVirtualList(container, groupedConversations);
            } else {
                renderNormalList(container, groupedConversations);
            }
        }

        function renderVirtualList(container, conversations) {
            const { itemHeight, visibleItems } = appState.state.virtualScrolling;
            const totalHeight = conversations.length * itemHeight;
            
            container.innerHTML = `
                <div style="height: ${totalHeight}px; position: relative;">
                    <div id="virtual-items-container" style="position: absolute; top: 0; left: 0; width: 100%;"></div>
                </div>
            `;

            const virtualItemsContainer = document.getElementById('virtual-items-container');
            
            function updateVisibleItems() {
                const scrollTop = container.scrollTop;
                const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) -1); // Renderizar um pouco antes
                const endIndex = Math.min(startIndex + visibleItems + 2, conversations.length); // Renderizar um pouco depois
                
                const fragment = document.createDocumentFragment();
                for (let i = startIndex; i < endIndex; i++) {
                    const conversation = conversations[i];
                    const top = i * itemHeight;
                    const itemEl = renderConversationItem(conversation, i, top, true);
                    fragment.appendChild(itemEl);
                }
                
                virtualItemsContainer.innerHTML = ''; // Limpar antes de adicionar
                virtualItemsContainer.appendChild(fragment);
                virtualItemsContainer.style.transform = `translateY(${startIndex * itemHeight}px)`;
            }
            
            // Remover listener antigo se existir para evitar duplicação
            if (container._scrollListener) {
                container.removeEventListener('scroll', container._scrollListener);
            }
            container._scrollListener = throttle(updateVisibleItems, 16);
            container.addEventListener('scroll', container._scrollListener, { passive: true });
            
            requestAnimationFrame(updateVisibleItems); // Chamada inicial
        }

        function renderNormalList(container, conversations) {
            const fragment = document.createDocumentFragment();
            conversations.forEach((conversation, index) => {
                fragment.appendChild(renderConversationItem(conversation, index, null, false));
            });
            container.innerHTML = '';
            container.appendChild(fragment);
        }

        function renderConversationItem(group, index, top = null, isVirtual = false) {
            const cacheKey = `convItem_${group.phone}_${group.lastTime?.toISOString()}_${(appState.state.conversationTags[group.conversations[0]?.id] || []).join(',')}`;
            
            if (!isVirtual && cache.renderedConversationItems.has(cacheKey)) {
                 const cachedEl = cache.renderedConversationItems.get(cacheKey).cloneNode(true);
                 if (appState.state.currentConversation?.phone === group.phone) {
                    cachedEl.classList.add('active');
                 } else {
                    cachedEl.classList.remove('active');
                 }
                 return cachedEl;
            }

            const itemDiv = document.createElement('div');
            itemDiv.className = `conversation-item virtual-item fade-in`;
            if (appState.state.currentConversation?.phone === group.phone) {
                itemDiv.classList.add('active');
            }
            itemDiv.dataset.phone = group.phone;
            itemDiv.style.animationDelay = `${Math.min(index * 0.03, 0.5)}s`; // Reduzir delay
            
            if (isVirtual && top !== null) {
                // Para virtual scrolling, o posicionamento é feito pelo container
            } else if (top !== null) {
                 itemDiv.style.position = 'absolute';
                 itemDiv.style.top = `${top}px`;
                 itemDiv.style.width = '100%';
            }


            itemDiv.onclick = () => selectConversation(group.phone);
            
            const tagsArray = Array.from(group.tags);
            const qualityIndicator = getQualityIndicator(group.avgQuality);
            
            itemDiv.innerHTML = `
                <div class="conversation-header">
                    <div class="conversation-info">
                        <div class="conversation-name">
                            <span class="conversation-avatar"></span>
                            ${escapeHtml(group.name)}
                            ${qualityIndicator}
                        </div>
                        <div class="conversation-phone">${formatPhoneNumber(group.phone)}</div>
                    </div>
                    <div class="conversation-meta">
                        <div class="conversation-time">${formatRelativeTime(group.lastTime)}</div>
                        <div class="conversation-count">${group.totalInteractions}</div>
                    </div>
                </div>
                <div class="conversation-preview">${truncateText(group.lastMessage, 60)}</div>
                <div class="conversation-tags">
                    ${tagsArray.slice(0, 3).map(tag => 
                        `<span class="tag ${getTagClass(tag)}">${escapeHtml(tag)}</span>`
                    ).join('')}
                    ${tagsArray.length > 3 ? 
                        `<span class="tag">+${tagsArray.length - 3}</span>` : ''}
                </div>
            `;
            if (!isVirtual) {
                cache.renderedConversationItems.set(cacheKey, itemDiv.cloneNode(true));
            }
            return itemDiv;
        }

        function groupConversationsByPhoneOptimized(conversations) {
            const grouped = new Map();
            
            conversations.forEach(conv => {
                const phone = conv.telefone || 'Sem telefone';
                
                if (!grouped.has(phone)) {
                    grouped.set(phone, {
                        phone: phone,
                        name: conv.nome || 'Anônimo',
                        conversations: [],
                        lastMessage: '',
                        lastTime: new Date(0), // Initialize with a very old date
                        tags: new Set(),
                        avgQuality: null,
                        totalInteractions: 0,
                        avgTemperature: 0,
                        totalWords: 0
                    });
                }
                
                const group = grouped.get(phone);
                group.conversations.push(conv);
                group.totalInteractions++;
                group.totalWords += conv.wordCount || 0;
                
                const messageTime = new Date(`${conv.data}T${conv.hora}`); // Ensure ISO format for correct date parsing
                if (!group.lastTime || messageTime > group.lastTime) {
                    group.lastTime = messageTime;
                    group.lastMessage = conv.pergunta_cliente || conv.reposta_ia;
                }

                if (conv.qualidade_resposta) {
                    group.tags.add(conv.qualidade_resposta);
                }
                
                (appState.state.conversationTags[conv.id] || []).forEach(tag => group.tags.add(tag));
            });

            return Array.from(grouped.values())
                .map(group => {
                    const qualities = group.conversations.map(c => c.qualidade_resposta).filter(Boolean);
                    if (qualities.length > 0) {
                        const scores = qualities.map(q => getScoreFromQuality(q));
                        group.avgQuality = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
                    }
                    
                    const temperatures = group.conversations.map(c => c.temperature || 0).filter(t => t > 0);
                    if (temperatures.length > 0) {
                        group.avgTemperature = Math.round(temperatures.reduce((a, b) => a + b, 0) / temperatures.length);
                    }
                    
                    return group;
                })
                .sort((a, b) => b.lastTime.getTime() - a.lastTime.getTime());
        }

        function selectConversation(phone) {
            const cacheKey = `convDetail_${phone}`;
            if (cache.renderedMessages.has(cacheKey)) {
                const cached = cache.renderedMessages.get(cacheKey);
                if (Date.now() - (cached.timestamp || 0) < 180000) { // Cache de 3 minutos para detalhes
                    appState.setState({ currentConversation: cached.data });
                    updateActiveConversation();
                    renderChatHeaderOptimized();
                    document.getElementById('messages-container').innerHTML = cached.html;
                    document.getElementById('chat-header').style.display = 'flex';
                    scrollToBottom('messages-container');
                    return;
                }
            }

            const groupedConversations = groupConversationsByPhoneOptimized(appState.state.allConversations);
            const selectedConversation = groupedConversations.find(g => g.phone === phone);
            
            if (selectedConversation) {
                appState.setState({ currentConversation: selectedConversation });
                updateActiveConversation();
                renderChatHeaderOptimized();
                renderMessagesOptimized(true); // Pass true to cache the result
                document.getElementById('chat-header').style.display = 'flex';
                trackConversationView(phone);
            }
        }

        function updateActiveConversation() {
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.toggle('active', item.dataset.phone === appState.state.currentConversation?.phone);
            });
        }

        function renderChatHeaderOptimized() {
            const conv = appState.state.currentConversation;
            if (!conv) return;

            document.getElementById('chat-name').textContent = conv.name;
            document.getElementById('chat-phone').innerHTML = `
                <div class="status-indicator"></div>
                ${formatPhoneNumber(conv.phone)} • ${conv.totalInteractions} interações
                ${conv.avgQuality !== null ? `• ${conv.avgQuality}% qualidade` : '• Não avaliada'}
                ${conv.totalWords ? `• ${conv.totalWords} palavras` : ''}
            `;
            document.getElementById('chat-avatar').textContent = conv.name.charAt(0).toUpperCase();
        }

        function renderMessagesOptimized(shouldCache = false) {
            const currentConv = appState.state.currentConversation;
            if (!currentConv) return;

            const container = document.getElementById('messages-container');
            const conversations = currentConv.conversations.sort((a, b) => 
                new Date(`${a.data}T${a.hora}`) - new Date(`${b.data}T${b.hora}`)
            );

            const fragment = document.createDocumentFragment();
            let currentDate = '';

            conversations.forEach((conv, index) => {
                const messageDate = formatDate(conv.data);
                if (messageDate !== currentDate) {
                    currentDate = messageDate;
                    const dateDiv = document.createElement('div');
                    dateDiv.className = 'message-date';
                    dateDiv.innerHTML = `<span class="date-badge">${messageDate}</span>`;
                    fragment.appendChild(dateDiv);
                }
                fragment.appendChild(createMessageElement(conv, 'client', index));
                fragment.appendChild(createMessageElement(conv, 'ia', index));
            });

            container.innerHTML = '';
            container.appendChild(fragment);
            
            if (shouldCache) {
                cache.renderedMessages.set(`convDetail_${currentConv.phone}`, {
                    data: currentConv,
                    html: container.innerHTML,
                    timestamp: Date.now()
                });
            }
            scrollToBottom('messages-container');
        }
        
        function scrollToBottom(elementId) {
            requestAnimationFrame(() => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.scrollTop = element.scrollHeight;
                }
            });
        }


        function createMessageElement(conv, type, index) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            // messageDiv.style.animationDelay = `${Math.min(index * 0.03, 0.5)}s`; // Pode ser removido se causar lentidão

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';

            if (type === 'client') {
                bubbleDiv.innerHTML = `
                    <div class="message-content">${conv.pergunta_cliente_formatted || 'Mensagem não disponível'}</div>
                    <div class="message-time">
                        ${formatTime(conv.hora)}
                        <i class="fas fa-check-double message-status"></i>
                    </div>
                `;
            } else { // IA
                bubbleDiv.classList.add('glow-on-hover');
                bubbleDiv.dataset.messageId = conv.id;
                bubbleDiv.onclick = () => selectMessage(conv.id);

                const qualityIcon = getQualityIcon(conv.qualidade_resposta);
                const temperature = conv.temperature || 0;
                const wordCount = conv.wordCount || 0;
                
                bubbleDiv.innerHTML = `
                    <div class="message-sender">
                        <i class="fas fa-robot"></i>
                        IA Assistant ${qualityIcon}
                        <div class="temperature-indicator">
                            <i class="fas fa-thermometer-half"></i>
                            <div class="temperature-bar">
                                <div class="temperature-fill" style="width: ${temperature}%"></div>
                            </div>
                            ${temperature}%
                        </div>
                    </div>
                    <div class="message-content">${conv.reposta_ia_formatted || 'Resposta não disponível'}</div>
                    <div class="message-time">
                        ${formatTime(conv.hora)}
                        <i class="fas fa-eye" title="Clique para validar"></i>
                    </div>
                    <div class="word-count">${wordCount} palavras</div>
                `;
            }
            messageDiv.appendChild(bubbleDiv);
            return messageDiv;
        }

        function selectMessage(messageId) {
            const selectedMessage = appState.state.allConversations.find(c => c.id === messageId);
            
            if (selectedMessage) {
                appState.setState({ selectedMessage });
                
                document.querySelectorAll('.message.ia .message-bubble').forEach(bubble => {
                    bubble.classList.toggle('selected', bubble.dataset.messageId == messageId);
                });
                
                renderValidationPanelOptimized();
                if (document.getElementById('validation-panel').classList.contains('hidden')) {
                    toggleValidationPanel();
                }
                showNotification('Mensagem selecionada para validação', 'info');
            }
        }

        function renderValidationPanelOptimized() {
            const message = appState.state.selectedMessage;
            if (!message) {
                 document.getElementById('validation-content').innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-clipboard-list"></i>
                        <h3>Selecione uma mensagem</h3>
                        <p>Clique em uma mensagem da IA para iniciar a validação e adicionar feedback</p>
                    </div>`;
                return;
            }

            const container = document.getElementById('validation-content');
            const tags = appState.state.conversationTags[message.id] || [];
            const qualityScore = getScoreFromQuality(message.qualidade_resposta);

            container.innerHTML = `
                <div class="validation-section bounce-in">
                    <h4><i class="fas fa-star"></i> Avaliação da Resposta</h4>
                    <div class="validation-grid">
                        <div class="validation-field">
                            <label for="quality-select">Qualidade da Resposta:</label>
                            <select id="quality-select" onchange="updateValidationPreview()">
                                <option value="">Selecione uma avaliação...</option>
                                <option value="boa" ${message.qualidade_resposta === 'boa' ? 'selected' : ''}>✅ Excelente</option>
                                <option value="regular" ${message.qualidade_resposta === 'regular' ? 'selected' : ''}>⚠️ Regular</option>
                                <option value="ruim" ${message.qualidade_resposta === 'ruim' ? 'selected' : ''}>❌ Ruim</option>
                            </select>
                        </div>
                        <div class="validation-field">
                            <label for="score-slider">Pontuação (0-100):</label>
                            <input type="range" id="score-slider" min="0" max="100" value="${qualityScore}" oninput="updateScoreDisplay(this.value)">
                            <div id="score-display" class="score-display">${qualityScore}/100</div>
                        </div>
                    </div>
                </div>

                <div class="validation-section">
                    <h4><i class="fas fa-chart-line"></i> Análise Automática</h4>
                    <div class="auto-analysis">
                        ${generateAutoAnalysisOptimized(message)}
                    </div>
                </div>

                <div class="validation-section">
                    <h4><i class="fas fa-comment-alt"></i> Feedback Detalhado</h4>
                    <div class="validation-grid">
                        <div class="validation-field">
                            <label for="observation-input">Observações Técnicas:</label>
                            <textarea id="observation-input" placeholder="Aspectos técnicos, precisão..." rows="4" oninput="updateWordCount(this, 'obs-count')">${escapeHtml(message.observacao || '')}</textarea>
                            <div class="word-count" id="obs-count">${countWords(message.observacao || '')} palavras</div>
                        </div>
                        <div class="validation-field">
                            <label for="feedback-input">Feedback para Melhoria:</label>
                            <textarea id="feedback-input" placeholder="Sugestões para a IA..." rows="4" oninput="updateWordCount(this, 'feedback-count')">${escapeHtml(message.feedback || '')}</textarea>
                            <div class="word-count" id="feedback-count">${countWords(message.feedback || '')} palavras</div>
                        </div>
                    </div>
                </div>

                <div class="validation-section">
                    <h4><i class="fas fa-tags"></i> Tags e Categorias</h4>
                    <div class="tags-container" id="tags-container-dropzone" ondrop="dropTag(event)" ondragover="allowDrop(event)">
                        ${tags.map(tag => `
                            <span class="tag-item" draggable="true" ondragstart="dragTag(event, '${escapeHtml(tag)}')">
                                <i class="fas fa-tag"></i> ${escapeHtml(tag)}
                                <button class="remove-tag" onclick="removeTag(appState.state.selectedMessage.id, '${escapeHtml(tag)}')"><i class="fas fa-times"></i></button>
                            </span>`).join('')}
                        ${tags.length === 0 ? '<p class="empty-tags-message" style="color: var(--text-muted); font-style: italic;">Nenhuma tag adicionada</p>' : ''}
                    </div>
                    
                    <div class="tag-suggestions">
                        <label>Tags Sugeridas:</label>
                        <div class="suggested-tags">
                            ${getSuggestedTagsOptimized(message).map(tag => `
                                <button class="suggested-tag" onclick="addSuggestedTag('${escapeHtml(tag)}')"><i class="fas fa-plus"></i> ${escapeHtml(tag)}</button>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="tag-input">
                        <input type="text" id="new-tag-input" placeholder="Nova tag..." onkeypress="handleTagInput(event)" list="common-tags-datalist">
                        <datalist id="common-tags-datalist">
                            <option value="atendimento"><option value="produto"><option value="suporte"><option value="vendas">
                            <option value="tecnico"><option value="urgente"><option value="satisfeito"><option value="insatisfeito">
                            <option value="complexo"><option value="simples"><option value="rapido"><option value="detalhado">
                        </datalist>
                        <button class="add-tag-btn" onclick="addTag()"><i class="fas fa-plus"></i> Adicionar</button>
                    </div>
                </div>

                <div class="validation-actions">
                    <button class="btn btn-secondary" onclick="resetValidation()"><i class="fas fa-undo"></i> Resetar</button>
                    <button class="btn btn-secondary" onclick="saveValidationDraft()"><i class="fas fa-save"></i> Salvar Rascunho</button>
                    <button class="btn btn-primary" onclick="copyToClipboard()"><i class="fas fa-copy"></i> Copiar Análise</button>
                    <button class="save-btn" onclick="saveValidation()"><i class="fas fa-check"></i> Confirmar Validação</button>
                </div>
            `;
        }

        function generateAutoAnalysisOptimized(message) {
            const content = message.reposta_ia || '';
            const wordCount = message.wordCount || countWords(content);
            const hasQuestions = content.includes('?');
            const hasLinks = /https?:\/\/[^\s]+/.test(content);
            const sentiment = message.sentiment || analyzeSentiment(content);
            const temperature = message.temperature || calculateTemperature(content);
            const hasCode = /```([\s\S]*?)```/.test(content); // Corrigido regex para ```
            const hasFormatting = /\*|_|~|`[^`]+`/.test(content); // Corrigido regex para formatação
            
            return `
                <div class="analysis-grid">
                    <div class="analysis-item"><i class="fas fa-align-left"></i><span>Palavras: ${wordCount}</span></div>
                    <div class="analysis-item"><i class="fas fa-question-circle"></i><span>Perguntas: ${hasQuestions ? 'Sim' : 'Não'}</span></div>
                    <div class="analysis-item"><i class="fas fa-link"></i><span>Links: ${hasLinks ? 'Sim' : 'Não'}</span></div>
                    <div class="analysis-item"><i class="fas fa-heart"></i><span>Tom: ${sentiment}</span></div>
                    <div class="analysis-item"><i class="fas fa-thermometer-half"></i><span>Complexidade: ${temperature}%</span></div>
                    <div class="analysis-item"><i class="fas fa-code"></i><span>Código: ${hasCode ? 'Sim' : 'Não'}</span></div>
                    <div class="analysis-item"><i class="fas fa-bold"></i><span>Formatação: ${hasFormatting ? 'Sim' : 'Não'}</span></div>
                    <div class="analysis-item"><i class="fas fa-clock"></i><span>Leitura: ~${Math.ceil(wordCount / 200)}min</span></div>
                </div>
            `;
        }

        function getSuggestedTagsOptimized(message) {
            const suggestions = new Set();
            const content = `${message.pergunta_cliente || ''} ${message.reposta_ia || ''}`.toLowerCase();
            
            const patterns = {
                'suporte': /\b(problema|erro|bug|falha|ajuda|suporte)\b/i,
                'vendas': /\b(compra|preço|valor|custo|pagamento|venda)\b/i,
                'produto': /\b(produto|item|mercadoria|artigo)\b/i,
                'urgente': /\b(urgente|rápido|imediato|pressa)\b/i,
                'satisfeito': /\b(obrigad[oa]|satisfeit[oa]|ótimo|excelente|bom|perfeito|maravilhoso)\b/i,
                'insatisfeito': /\b(ruim|péssimo|insatisfeit[oa]|reclamação|decepcionado)\b/i,
                'tecnico': /\b(api|sql|código|programação|sistema|técnico)\b/i,
                'complexo': /\b(complexo|complicado|difícil|detalhado)\b/i,
                'simples': /\b(simples|fácil|básico|direto)\b/i,
                'detalhado': /\b(detalhado|completo|extenso|longo)\b/i
            };

            for (const [tag, pattern] of Object.entries(patterns)) {
                if (pattern.test(content)) {
                    suggestions.add(tag);
                }
            }

            const temp = message.temperature || 0;
            if (temp > 70) suggestions.add('complexo');
            else if (temp < 30) suggestions.add('simples');
            
            const wordCount = message.wordCount || 0;
            if (wordCount > 100) suggestions.add('detalhado');
            else if (wordCount < 20) suggestions.add('rapido');

            return Array.from(suggestions).slice(0, 6);
        }

        function handleSearchOptimized() {
            applyFiltersOptimized(); // A busca agora é parte da filtragem geral
        }

        function applyFiltersOptimized() {
            const filters = {
                search: document.getElementById('search-input').value.toLowerCase().trim(),
                quality: document.getElementById('filter-quality').value,
                date: document.getElementById('filter-date').value,
                period: document.getElementById('filter-period').value,
                tag: document.getElementById('filter-tag').value.toLowerCase().trim()
            };

            let filtered = appState.state.allConversations;

            if (filters.search.length >= 2) {
                filtered = filtered.filter(conv => 
                    `${conv.nome || ''} ${conv.telefone || ''} ${conv.pergunta_cliente || ''} ${conv.reposta_ia || ''}`.toLowerCase().includes(filters.search)
                );
            }

            if (filters.quality) {
                filtered = filtered.filter(conv => 
                    filters.quality === 'pendente' ? !conv.qualidade_resposta : conv.qualidade_resposta === filters.quality
                );
            }

            if (filters.date) {
                filtered = filtered.filter(conv => conv.data === filters.date);
            }

            if (filters.period) {
                const now = new Date();
                filtered = filtered.filter(conv => {
                    const convDate = new Date(conv.data);
                    switch(filters.period) {
                        case 'today': return convDate.toDateString() === now.toDateString();
                        case 'week': 
                            const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
                            return convDate >= weekStart;
                        case 'month': return convDate.getMonth() === now.getMonth() && convDate.getFullYear() === now.getFullYear();
                        default: return true;
                    }
                });
            }

            if (filters.tag) {
                filtered = filtered.filter(conv => 
                    (appState.state.conversationTags[conv.id] || []).some(tag => tag.toLowerCase().includes(filters.tag))
                );
            }

            appState.setState({ filteredConversations: filtered });
            renderConversationsListOptimized();
            updateFilterCount();
        }

        function addTag() {
            const input = document.getElementById('new-tag-input');
            const tag = input.value.trim().toLowerCase();
            const messageId = appState.state.selectedMessage?.id;
            
            if (tag && messageId && tag.length >= 2) {
                const currentTags = appState.state.conversationTags[messageId] || [];
                if (!currentTags.includes(tag)) {
                    const newConversationTags = { ...appState.state.conversationTags };
                    newConversationTags[messageId] = [...currentTags, tag];
                    
                    appState.setState({ conversationTags: newConversationTags });
                    input.value = '';
                    renderValidationPanelOptimized(); // Re-render panel to show new tag
                    // Potentially re-render conversation list if tags are shown there
                    const convItem = document.querySelector(`.conversation-item[data-phone="${appState.state.currentConversation.phone}"]`);
                    if (convItem) { // If current conversation is visible, update its tags
                        // This is a simplification; a full re-render of the item might be better
                        // Or, update the specific conversation group and re-render that item
                    }
                    showNotification(`Tag "${tag}" adicionada`, 'success');
                } else {
                    showNotification('Esta tag já existe', 'warning');
                }
            } else if (tag.length < 2) {
                showNotification('Tag deve ter pelo menos 2 caracteres', 'warning');
            }
        }

        function addSuggestedTag(tag) {
            const messageId = appState.state.selectedMessage?.id;
            if (messageId) {
                const currentTags = appState.state.conversationTags[messageId] || [];
                if (!currentTags.includes(tag)) {
                    const newConversationTags = { ...appState.state.conversationTags };
                    newConversationTags[messageId] = [...currentTags, tag];
                    appState.setState({ conversationTags: newConversationTags });
                    renderValidationPanelOptimized();
                    showNotification(`Tag "${tag}" adicionada`, 'success');
                }
            }
        }

        function removeTag(messageId, tagToRemove) {
            const newConversationTags = { ...appState.state.conversationTags };
            if (newConversationTags[messageId]) {
                newConversationTags[messageId] = newConversationTags[messageId].filter(t => t !== tagToRemove);
                appState.setState({ conversationTags: newConversationTags });
                renderValidationPanelOptimized();
                showNotification(`Tag "${tagToRemove}" removida`, 'info');
            }
        }

        function handleTagInput(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                addTag();
            }
        }

        async function saveValidation() {
            const selectedMsg = appState.state.selectedMessage;
            if (!selectedMsg) return;

            const quality = document.getElementById('quality-select')?.value;
            const observation = document.getElementById('observation-input')?.value;
            const feedback = document.getElementById('feedback-input')?.value;

            if (!quality) {
                showNotification('Por favor, selecione uma qualidade para a resposta', 'warning');
                return;
            }

            const saveButton = document.querySelector('.save-btn');
            try {
                showLoadingButton(saveButton, true, 'Salvando...');
                
                const response = await fetch(`${API_CONFIG.SUPABASE_URL}/rest/v1/maqpeças_interacoes?id=eq.${selectedMsg.id}`, {
                    method: 'PATCH',
                    headers: {
                        'apikey': API_CONFIG.SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${API_CONFIG.SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation' // Para obter o registro atualizado
                    },
                    body: JSON.stringify({
                        qualidade_resposta: quality,
                        observacao: observation,
                        feedback: feedback
                    })
                });

                if (response.ok) {
                    const updatedRecord = await response.json(); // Supabase retorna um array
                    const updatedMessage = updatedRecord[0];

                    const updatedConversations = appState.state.allConversations.map(conv => 
                        conv.id === selectedMsg.id ? { ...conv, ...updatedMessage } : conv
                    );
                    
                    appState.setState({
                        allConversations: updatedConversations,
                        // Re-filtrar se necessário, ou atualizar o item em filteredConversations
                        filteredConversations: appState.state.filteredConversations.map(conv => 
                            conv.id === selectedMsg.id ? { ...conv, ...updatedMessage } : conv
                        ),
                        selectedMessage: { ...selectedMsg, ...updatedMessage }
                    });
                    
                    cache.conversations.delete('conversations_data'); // Invalidar cache de dados gerais
                    cache.renderedMessages.delete(`convDetail_${selectedMsg.telefone}`); // Invalidar cache de mensagens da conversa
                    
                    saveToLocalStorage(); // Salvar tags e outras preferências
                    
                    showNotification('✅ Validação salva com sucesso!', 'success');
                    renderConversationsListOptimized(); // Re-render lista de conversas
                    renderMessagesOptimized(); // Re-render mensagens da conversa atual
                    updateStats();
                    
                    trackValidation(selectedMsg.id, quality);
                } else {
                    const errorData = await response.json();
                    throw new Error(`Erro ${response.status}: ${errorData.message || response.statusText}`);
                }
            } catch (error) {
                console.error('Erro ao salvar validação:', error);
                showNotification(`❌ Erro ao salvar: ${error.message}`, 'error');
            } finally {
                showLoadingButton(saveButton, false, '<i class="fas fa-check"></i> Confirmar Validação');
            }
        }

        function toggleTheme() {
            const body = document.body;
            const newTheme = body.dataset.theme === 'dark' ? 'light' : 'dark';
            body.dataset.theme = newTheme;
            appState.setState({ theme: newTheme });
            
            const themeIcon = document.getElementById('theme-icon');
            themeIcon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            
            saveToLocalStorage();
            showNotification(`Tema ${newTheme === 'dark' ? 'escuro' : 'claro'} ativado`, 'info');
        }

        function handleSystemThemeChange(e) {
            if (!localStorage.getItem('crm_theme_user_preference')) { // Check for user override
                const newTheme = e.matches ? 'dark' : 'light';
                document.body.dataset.theme = newTheme;
                appState.setState({ theme: newTheme });
                const themeIcon = document.getElementById('theme-icon');
                themeIcon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        function escapeHtml(text) {
            if (typeof text !== 'string') return '';
            return text
                .replace(/&/g, "&")
                .replace(/</g, "<")
                .replace(/>/g, ">")
                .replace(/"/g, """)
                .replace(/'/g, "&#039;");
        }

        function updateWordCount(textarea, counterId) {
            const count = countWords(textarea.value);
            document.getElementById(counterId).textContent = `${count} palavras`;
        }
        
        function renderConversationSkeleton() {
            return `
                <div class="conversation-item skeleton">
                    <div class="skeleton-line" style="height: 20px; width: 70%; margin-bottom: 8px;"></div>
                    <div class="skeleton-line" style="height: 14px; width: 50%; margin-bottom: 8px;"></div>
                    <div class="skeleton-line" style="height: 12px; width: 80%;"></div>
                </div>
            `;
        }
        
        function renderMessageSkeleton() {
             return `
                <div class="message skeleton">
                    <div class="message-bubble" style="width: 60%;">
                        <div class="skeleton-line" style="height: 16px; margin-bottom: 6px;"></div>
                        <div class="skeleton-line" style="height: 14px; width: 80%; margin-bottom: 6px;"></div>
                        <div class="skeleton-line" style="height: 10px; width: 30%; float: right;"></div>
                    </div>
                </div>
            `;
        }

        function showSkeletonLoading(containerId, count, skeletonRenderer) {
            const container = document.getElementById(containerId);
            if (!container) return;
            let html = '';
            for (let i = 0; i < count; i++) {
                html += skeletonRenderer();
            }
            container.innerHTML = html;
        }


        function throttle(func, limit) {
            let inThrottle;
            let lastFunc;
            let lastRan;
            return function() {
                const context = this;
                const args = arguments;
                if (!inThrottle) {
                    func.apply(context, args);
                    lastRan = Date.now();
                    inThrottle = true;
                    setTimeout(() => {
                        inThrottle = false;
                        if (lastFunc) {
                            lastFunc();
                            lastFunc = null;
                        }
                    }, limit);
                } else {
                    lastFunc = () => {
                        if ((Date.now() - lastRan) >= limit) {
                            func.apply(context, args);
                            lastRan = Date.now();
                        }
                    };
                }
            }
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func.apply(this, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function handleScroll(event) {
            // Exemplo: Lazy load de imagens ou conteúdo adicional
            // Este handler é genérico, a lógica específica de virtual scroll está em renderVirtualList
            const { scrollTop, scrollHeight, clientHeight } = event.target;
            if (scrollTop + clientHeight >= scrollHeight - 200) { // Perto do fim
                // console.log('Perto do fim do scroll');
                // loadMoreItems(); // Se houver paginação
            }
        }

        function updateValidationPreview() {
            const quality = document.getElementById('quality-select')?.value;
            const score = getScoreFromQuality(quality);
            const slider = document.getElementById('score-slider');
            if (slider) slider.value = score;
            updateScoreDisplay(score);
        }

        function updateScoreDisplay(score) {
            const display = document.getElementById('score-display');
            if (display) display.textContent = `${score}/100`;
        }

        function resetValidation() {
            if (confirm('Tem certeza que deseja resetar todas as alterações neste painel?')) {
                renderValidationPanelOptimized(); // Re-render com dados originais da mensagem
                showNotification('Validação resetada para o estado original', 'info');
            }
        }

        function saveValidationDraft() {
            const msg = appState.state.selectedMessage;
            if (!msg) return;
            
            const draft = {
                messageId: msg.id,
                quality: document.getElementById('quality-select')?.value || '',
                observation: document.getElementById('observation-input')?.value || '',
                feedback: document.getElementById('feedback-input')?.value || '',
                tags: appState.state.conversationTags[msg.id] || [],
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem(`validation_draft_${msg.id}`, JSON.stringify(draft));
            showNotification('Rascunho salvo localmente', 'success');
        }
        
        function loadValidationDraft(messageId) {
            const draftString = localStorage.getItem(`validation_draft_${messageId}`);
            if (draftString) {
                const draft = JSON.parse(draftString);
                // Popular campos do painel de validação com 'draft'
                // Ex: document.getElementById('quality-select').value = draft.quality;
                // ... e assim por diante para observações, feedback, tags.
                // Depois, re-renderizar o painel ou atualizar os campos diretamente.
                showNotification('Rascunho carregado', 'info');
            }
        }


        function copyToClipboard() {
            const msg = appState.state.selectedMessage;
            if (!msg) return;
            
            const analysisHTML = generateAutoAnalysisOptimized(msg);
            // Converter HTML para texto simples para clipboard
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = analysisHTML;
            const text = `Análise da mensagem ID: ${msg.id}\n\n${tempDiv.textContent || tempDiv.innerText || ""}`;
            
            navigator.clipboard.writeText(text.replace(/\s\s+/g, '\n').trim()).then(() => {
                showNotification('Análise copiada', 'success');
            }).catch(err => {
                showNotification('Erro ao copiar', 'error');
                console.error('Erro ao copiar para clipboard:', err);
            });
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    showNotification(`Erro ao entrar em tela cheia: ${err.message}`, 'warning');
                });
            } else {
                document.exitFullscreen();
            }
        }

        function showSettings() {
            // Implementar modal de configurações
            showNotification('Funcionalidade de Configurações em desenvolvimento.', 'info');
        }

        function formatTime(timeStr) { // timeStr no formato "HH:MM:SS"
            if (!timeStr || typeof timeStr !== 'string') return '';
            return timeStr.substring(0, 5); // Retorna "HH:MM"
        }

        function formatDate(dateStr) { // dateStr no formato "YYYY-MM-DD"
            if (!dateStr) return '';
            const today = new Date();
            const messageDate = new Date(`${dateStr}T00:00:00`); // Adicionar hora para evitar problemas de fuso
            
            today.setHours(0,0,0,0); // Normalizar data de hoje

            if (messageDate.toDateString() === today.toDateString()) return 'Hoje';
            
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);
            if (messageDate.toDateString() === yesterday.toDateString()) return 'Ontem';
            
            return messageDate.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: 'numeric' });
        }

        function formatRelativeTime(date) {
            if (!date || !(date instanceof Date) || isNaN(date)) return '';
            const now = new Date();
            const diffSeconds = Math.round((now - date) / 1000);
            
            if (diffSeconds < 60) return 'agora';
            const diffMinutes = Math.round(diffSeconds / 60);
            if (diffMinutes < 60) return `${diffMinutes}m`;
            const diffHours = Math.round(diffMinutes / 60);
            if (diffHours < 24) return `${diffHours}h`;
            const diffDays = Math.round(diffHours / 24);
            if (diffDays < 7) return `${diffDays}d`;
            
            return date.toLocaleDateString('pt-BR', { day: '2-digit', month: 'short' });
        }

        function formatPhoneNumber(phone) {
            if (!phone || phone === 'Sem telefone') return phone;
            const cleaned = String(phone).replace(/\D/g, '');
            if (cleaned.length === 11) return `(${cleaned.slice(0,2)}) ${cleaned.slice(2,7)}-${cleaned.slice(7)}`;
            if (cleaned.length === 10) return `(${cleaned.slice(0,2)}) ${cleaned.slice(2,6)}-${cleaned.slice(6)}`;
            return phone; // Retornar original se não corresponder aos padrões comuns
        }

        function truncateText(text, maxLength) {
            if (typeof text !== 'string') return '';
            return text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
        }

        function getTagClass(tag) {
            const qualityMap = {
                'boa': 'quality-boa',
                'regular': 'quality-regular',
                'ruim': 'quality-ruim',
                'pendente': 'pendente', // Adicionado para consistência
                'urgente': 'tag-urgent', // Exemplo, defina no CSS
                'satisfeito': 'tag-positive', // Exemplo
                'insatisfeito': 'tag-negative' // Exemplo
            };
            return qualityMap[tag?.toLowerCase()] || '';
        }

        function getQualityIcon(quality) {
            const iconMap = {
                'boa': '<i class="fas fa-check-circle" style="color: var(--gradient-success);"></i>',
                'regular': '<i class="fas fa-exclamation-circle" style="color: var(--gradient-warning);"></i>',
                'ruim': '<i class="fas fa-times-circle" style="color: var(--gradient-danger);"></i>',
            };
            return iconMap[quality?.toLowerCase()] || '<i class="fas fa-clock" style="color: var(--text-muted);"></i>';
        }

        function getQualityIndicator(avgQuality) {
            if (avgQuality === null || avgQuality === undefined) return '';
            if (avgQuality >= 80) return '<i class="fas fa-star" style="color: #ffd700;"></i>';
            if (avgQuality >= 60) return '<i class="fas fa-star-half-alt" style="color: #ffc107;"></i>';
            if (avgQuality > 0) return '<i class="far fa-star" style="color: #ff9800;"></i>';
            return '<i class="far fa-star" style="color: var(--text-muted);"></i>';
        }

        function getScoreFromQuality(quality) {
            const scoreMap = { 'boa': 85, 'regular': 60, 'ruim': 25 };
            return scoreMap[quality?.toLowerCase()] || 0;
        }

        function analyzeSentiment(text) {
            if (typeof text !== 'string' || !text.trim()) return 'Neutro';
            const lowerText = text.toLowerCase();
            // Palavras-chave mais robustas e pesos (simplificado)
            const positiveKeywords = /\b(obrigad[oa]|satisfeit[oa]|ótimo|excelente|bom|perfeito|maravilhoso|ador[oe]i|gostei|ajudou)\b/gi;
            const negativeKeywords = /\b(ruim|péssimo|problema|erro|insatisfeit[oa]|terrível|horrível|n[ãa]o gostei|n[ãa]o ajudou|decepcionad[oa])\b/gi;

            const positiveMatches = (lowerText.match(positiveKeywords) || []).length;
            const negativeMatches = (lowerText.match(negativeKeywords) || []).length;
            
            if (positiveMatches > negativeMatches) return 'Positivo';
            if (negativeMatches > positiveMatches) return 'Negativo';
            return 'Neutro';
        }

        function updateStats() {
            const conversations = appState.state.allConversations;
            const total = conversations.length;
            const pending = conversations.filter(c => !c.qualidade_resposta).length;
            const validated = total - pending;
            
            const qualities = conversations.map(c => c.qualidade_resposta).filter(Boolean);
            let avgQuality = 0;
            if (qualities.length > 0) {
                const scores = qualities.map(q => getScoreFromQuality(q));
                avgQuality = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
            }

            appState.setState({ stats: { total, pending, validated, avgQuality } });

            document.getElementById('total-conversations').textContent = total;
            document.getElementById('pending-validations').textContent = pending;
            document.getElementById('validated-count').textContent = validated;
            document.getElementById('avg-score').textContent = `${avgQuality}%`;
        }

        function updateFilterCount() {
            const activeFilters = [
                document.getElementById('search-input').value,
                document.getElementById('filter-quality').value,
                document.getElementById('filter-date').value,
                document.getElementById('filter-period').value,
                document.getElementById('filter-tag').value
            ].filter(Boolean).length; // Boolean para contar apenas os que têm valor

            document.getElementById('filter-count').textContent = activeFilters;
        }

        function clearAllFilters() {
            document.getElementById('search-input').value = '';
            document.getElementById('filter-quality').value = '';
            document.getElementById('filter-date').value = '';
            document.getElementById('filter-period').value = '';
            document.getElementById('filter-tag').value = '';
            
            appState.setState({ filteredConversations: [...appState.state.allConversations] });
            renderConversationsListOptimized();
            updateFilterCount();
            showNotification('Filtros limpos', 'info');
        }

        function toggleAdvancedFilters() {
            document.getElementById('advanced-filters').classList.toggle('active');
        }

        function toggleValidationPanel() {
            document.getElementById('validation-panel').classList.toggle('hidden');
        }

        function refreshConversations() {
            showNotification('Atualizando conversas...', 'info');
            cache.conversations.delete('conversations_data');
            cache.renderedConversationItems.clear();
            cache.renderedMessages.clear();
            loadConversationsOptimized();
        }

        function exportConversation() {
            const conv = appState.state.currentConversation;
            if (!conv) return;
            
            const data = {
                phone: conv.phone,
                name: conv.name,
                conversations: conv.conversations.map(c => ({ // Selecionar campos relevantes
                    id: c.id, data: c.data, hora: c.hora,
                    pergunta_cliente: c.pergunta_cliente, reposta_ia: c.reposta_ia,
                    qualidade_resposta: c.qualidade_resposta, observacao: c.observacao, feedback: c.feedback
                })),
                tags: appState.state.conversationTags, // Considerar apenas tags da conversa atual
                exportDate: new Date().toISOString(),
                stats: {
                    totalInteractions: conv.totalInteractions,
                    avgQuality: conv.avgQuality,
                    avgTemperature: conv.avgTemperature,
                    totalWords: conv.totalWords
                }
            };
            
            downloadJSON(data, `conversa_${conv.phone}_${formatDateForFilename(new Date())}.json`);
        }

        function exportAllData() {
            const data = {
                conversations: appState.state.allConversations.map(c => ({ // Selecionar campos
                    id: c.id, nome: c.nome, telefone: c.telefone, data: c.data, hora: c.hora,
                    pergunta_cliente: c.pergunta_cliente, reposta_ia: c.reposta_ia,
                    qualidade_resposta: c.qualidade_resposta, observacao: c.observacao, feedback: c.feedback
                })),
                tags: appState.state.conversationTags,
                stats: appState.state.stats,
                exportDate: new Date().toISOString(),
                version: '3.0.1' // Incrementar versão se houver mudanças no formato
            };
            
            downloadJSON(data, `crm_backup_completo_${formatDateForFilename(new Date())}.json`);
        }

        function downloadJSON(data, filename) {
            try {
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a); // Necessário para Firefox
                a.click();
                a.remove(); // Limpar
                URL.revokeObjectURL(url);
                showNotification(`${filename} exportado com sucesso`, 'success');
            } catch (error) {
                console.error("Erro ao exportar JSON:", error);
                showNotification("Erro ao exportar dados", "error");
            }
        }

        function formatDateForFilename(date) {
            return date.toISOString().split('T')[0].replace(/-/g, ''); // Mais compacto
        }

        function showNotification(message, type = 'info', duration = 4000) {
            const container = document.getElementById('notification-container') || createNotificationContainer();
            
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            
            const iconMap = {
                success: 'fas fa-check-circle', error: 'fas fa-exclamation-triangle',
                warning: 'fas fa-exclamation-circle', info: 'fas fa-info-circle'
            };
            notification.innerHTML = `<i class="${iconMap[type] || iconMap.info}"></i><span>${escapeHtml(message)}</span>`;
            
            container.appendChild(notification);
            requestAnimationFrame(() => notification.classList.add('show'));
            
            setTimeout(() => {
                notification.classList.remove('show');
                notification.addEventListener('transitionend', () => notification.remove(), { once: true });
            }, duration);
        }

        function createNotificationContainer() {
            let container = document.getElementById('notification-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'notification-container';
                // Estilos já definidos no CSS, mas podem ser adicionados aqui se necessário
                document.body.appendChild(container);
            }
            return container;
        }

        function saveToLocalStorage() {
            try {
                localStorage.setItem('crm_conversation_tags', JSON.stringify(appState.state.conversationTags));
                localStorage.setItem('crm_theme_user_preference', appState.state.theme); // Chave específica para preferência do usuário
                localStorage.setItem('crm_filter_presets', JSON.stringify(appState.state.filterPresets));
                localStorage.setItem('crm_last_save_timestamp', Date.now().toString());
            } catch (error) {
                console.error('Erro ao salvar no localStorage:', error);
                showNotification('Não foi possível salvar preferências locais.', 'warning');
            }
        }

        function loadSavedData() {
            try {
                const savedTags = localStorage.getItem('crm_conversation_tags');
                if (savedTags) appState.setState({ conversationTags: JSON.parse(savedTags) });
                
                const userThemePreference = localStorage.getItem('crm_theme_user_preference');
                const systemThemeDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                const initialTheme = userThemePreference || (systemThemeDark ? 'dark' : 'light');
                
                appState.setState({ theme: initialTheme });
                document.body.dataset.theme = initialTheme;
                const themeIcon = document.getElementById('theme-icon');
                if (themeIcon) themeIcon.className = initialTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
                
                const savedPresets = localStorage.getItem('crm_filter_presets');
                if (savedPresets) appState.setState({ filterPresets: JSON.parse(savedPresets) });

            } catch (error) {
                console.error('Erro ao carregar dados salvos:', error);
                localStorage.clear(); // Limpar localStorage corrompido
                showNotification('Preferências corrompidas, resetando.', 'warning');
            }
        }

        function autoSave() {
            if (!appState.state.isLoading) {
                saveToLocalStorage();
                // showNotification('Progresso salvo automaticamente', 'info', 2000); // Opcional
            }
        }

        function updateLoadingState(isLoading) {
            // A lógica de skeleton loading já é chamada onde necessário
            // Pode-se adicionar um spinner global se desejado
            document.body.classList.toggle('app-loading', isLoading);
        }

        function showLoadingButton(buttonElement, isLoading, loadingText = 'Carregando...') {
            if (!buttonElement) return;
            const originalContent = buttonElement.dataset.originalContent || buttonElement.innerHTML;
            if (!buttonElement.dataset.originalContent) {
                buttonElement.dataset.originalContent = originalContent;
            }

            if (isLoading) {
                buttonElement.disabled = true;
                buttonElement.innerHTML = `<div class="loading-spinner" style="width:1em; height:1em; border-width:2px; margin-right: 0.5em;"></div> ${loadingText}`;
            } else {
                buttonElement.disabled = false;
                buttonElement.innerHTML = originalContent;
            }
        }

        function trackConversationView(phone) {
            // Implementar envio para analytics (ex: Google Analytics, Mixpanel)
            console.log(`Analytics: Conversa visualizada - ${phone} em ${new Date().toISOString()}`);
        }

        function trackValidation(messageId, quality) {
            console.log(`Analytics: Validação salva - ID ${messageId}, Qualidade: ${quality} em ${new Date().toISOString()}`);
        }

        function filterByStatus(status) {
            clearAllFilters(); // Limpar outros filtros antes de aplicar o de status
            switch(status) {
                case 'pending':
                    document.getElementById('filter-quality').value = 'pendente';
                    break;
                case 'validated':
                    // Este caso é mais complexo, pois "validado" significa qualquer qualidade exceto pendente.
                    // A lógica de applyFiltersOptimized já lida com isso se 'filter-quality' estiver vazio.
                    // Para forçar "validados", precisaríamos de uma lógica customizada ou um filtro que exclua 'pendente'.
                    // Por ora, deixar que o usuário selecione uma qualidade específica ou todas.
                    // Ou, modificar applyFiltersOptimized para entender um valor especial como "any_validated".
                    const validatedConversations = appState.state.allConversations.filter(c => c.qualidade_resposta && c.qualidade_resposta !== 'pendente');
                     appState.setState({ filteredConversations: validatedConversations });
                     renderConversationsListOptimized();
                     updateFilterCount(); // Atualizar contagem de filtros ativos
                    return; 
                case 'all':
                default:
                    // clearAllFilters já foi chamado
                    return;
            }
            applyFiltersOptimized();
        }

        function showAnalytics() {
            showNotification('Analytics globais em desenvolvimento.', 'info');
        }

        function saveFilterPreset() {
            const presetName = prompt('Nome do preset de filtro:');
            if (presetName && presetName.trim()) {
                const preset = {
                    name: presetName.trim(),
                    search: document.getElementById('search-input').value,
                    quality: document.getElementById('filter-quality').value,
                    date: document.getElementById('filter-date').value,
                    period: document.getElementById('filter-period').value,
                    tag: document.getElementById('filter-tag').value,
                    timestamp: new Date().toISOString()
                };
                
                const updatedPresets = [...appState.state.filterPresets.filter(p => p.name !== preset.name), preset];
                appState.setState({ filterPresets: updatedPresets });
                saveToLocalStorage();
                showNotification(`Preset "${preset.name}" salvo`, 'success');
                updateFilterPresetsUI();
            } else if (presetName !== null) { // Se não cancelou, mas nome é inválido
                 showNotification('Nome do preset inválido.', 'warning');
            }
        }

        function loadFilterPreset(presetName) {
            const preset = appState.state.filterPresets.find(p => p.name === presetName);
            if (preset) {
                document.getElementById('search-input').value = preset.search || '';
                document.getElementById('filter-quality').value = preset.quality || '';
                document.getElementById('filter-date').value = preset.date || '';
                document.getElementById('filter-period').value = preset.period || '';
                document.getElementById('filter-tag').value = preset.tag || '';
                
                applyFiltersOptimized();
                showNotification(`Preset "${presetName}" carregado`, 'info');
            }
        }

        function deleteFilterPreset(presetName) {
            if (confirm(`Tem certeza que deseja excluir o preset "${presetName}"?`)) {
                const updatedPresets = appState.state.filterPresets.filter(p => p.name !== presetName);
                appState.setState({ filterPresets: updatedPresets });
                saveToLocalStorage();
                showNotification(`Preset "${presetName}" excluído`, 'info');
                updateFilterPresetsUI();
            }
        }

        function updateFilterPresetsUI() {
            // Exemplo: popular um <select> com os presets
            const presetsContainer = document.getElementById('filter-presets-container'); // Supondo que exista
            if (presetsContainer) {
                presetsContainer.innerHTML = ''; // Limpar
                appState.state.filterPresets.forEach(preset => {
                    const button = document.createElement('button');
                    button.textContent = preset.name;
                    button.onclick = () => loadFilterPreset(preset.name);
                    // Adicionar botão de delete também
                    presetsContainer.appendChild(button);
                });
            }
            console.log('Presets de filtro atualizados:', appState.state.filterPresets);
        }

        async function shareConversation() {
            const conv = appState.state.currentConversation;
            if (!conv) {
                showNotification('Nenhuma conversa selecionada para compartilhar.', 'warning');
                return;
            }

            const shareData = {
                title: `Conversa CRM: ${conv.name}`,
                text: `Detalhes da conversa com ${conv.name} (${formatPhoneNumber(conv.phone)}). Total de ${conv.totalInteractions} interações.`,
                url: window.location.href // URL atual, pode ser mais específico se houver deep linking
            };

            try {
                if (navigator.share) {
                    await navigator.share(shareData);
                    showNotification('Conversa compartilhada!', 'success');
                } else if (navigator.clipboard) {
                    await navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`);
                    showNotification('Link da conversa copiado!', 'success');
                } else {
                    showNotification('Compartilhamento não suportado neste navegador.', 'warning');
                }
            } catch (error) {
                console.error('Erro ao compartilhar/copiar:', error);
                if (error.name !== 'AbortError') { // Ignorar se o usuário cancelou
                    showNotification('Erro ao tentar compartilhar.', 'error');
                }
            }
        }

        function printConversation() {
            const conv = appState.state.currentConversation;
            if (!conv) {
                showNotification('Nenhuma conversa selecionada para impressão.', 'warning');
                return;
            }

            const printWindow = window.open('', '_blank', 'height=600,width=800');
            if (!printWindow) {
                showNotification('Bloqueador de pop-up impediu a impressão.', 'warning');
                return;
            }
            
            printWindow.document.write(generatePrintContent(conv));
            printWindow.document.close(); // Necessário para IE
            
            printWindow.onload = () => { // Esperar o conteúdo carregar
                printWindow.focus(); // Para Safari
                printWindow.print();
                // printWindow.close(); // Fechar automaticamente pode ser indesejado
            };
            showNotification('Preparando impressão...', 'info');
        }

        function generatePrintContent(conversation) {
            const messages = conversation.conversations.sort((a, b) => 
                new Date(`${a.data}T${a.hora}`) - new Date(`${b.data}T${b.hora}`)
            );

            // Usar CSS variáveis para consistência, se possível, ou definir estilos inline básicos
            return `
                <!DOCTYPE html>
                <html lang="pt-BR">
                <head>
                    <meta charset="UTF-8">
                    <title>Impressão da Conversa - ${escapeHtml(conversation.name)}</title>
                    <style>
                        body { font-family: 'Inter', sans-serif; margin: 20px; line-height: 1.6; color: #333; }
                        .print-header { border-bottom: 1px solid #ccc; padding-bottom: 15px; margin-bottom: 20px; }
                        .print-header h1 { font-size: 20px; margin: 0 0 5px 0; color: #00a884; }
                        .print-header p { font-size: 12px; margin: 3px 0; }
                        .message-group-print { margin-bottom: 15px; }
                        .message-print { padding: 8px 12px; border-radius: 8px; margin-bottom: 8px; max-width: 80%; word-wrap: break-word; }
                        .client-msg { background-color: #e6ffde; align-self: flex-end; margin-left: auto; }
                        .ia-msg { background-color: #f0f2f5; align-self: flex-start; }
                        .msg-sender { font-weight: bold; font-size: 11px; margin-bottom: 3px; }
                        .msg-content { font-size: 13px; }
                        .msg-time { font-size: 10px; color: #777; text-align: right; margin-top: 4px; }
                        .msg-quality { font-size: 10px; font-style: italic; color: #555; }
                        @media print {
                            body { margin: 1cm; }
                            .print-header, .message-group-print { page-break-inside: avoid; }
                        }
                    </style>
                </head>
                <body>
                    <div class="print-header">
                        <h1>Conversa com ${escapeHtml(conversation.name)}</h1>
                        <p><strong>Telefone:</strong> ${escapeHtml(formatPhoneNumber(conversation.phone))}</p>
                        <p><strong>Total de Interações:</strong> ${conversation.totalInteractions}</p>
                        <p><strong>Qualidade Média:</strong> ${conversation.avgQuality !== null ? conversation.avgQuality + '%' : 'Não avaliada'}</p>
                        <p><strong>Data de Impressão:</strong> ${new Date().toLocaleString('pt-BR')}</p>
                    </div>
                    
                    ${messages.map(msg => `
                        <div class="message-group-print">
                            <div class="message-print client-msg">
                                <div class="msg-sender">Cliente (${escapeHtml(formatTime(msg.hora))} - ${escapeHtml(formatDate(msg.data))})</div>
                                <div class="msg-content">${msg.pergunta_cliente_formatted || 'Mensagem não disponível'}</div>
                            </div>
                            <div class="message-print ia-msg">
                                <div class="msg-sender">IA Assistant (${escapeHtml(formatTime(msg.hora))})</div>
                                <div class="msg-content">${msg.reposta_ia_formatted || 'Resposta não disponível'}</div>
                                ${msg.qualidade_resposta ? `<div class="msg-quality">Qualidade: ${escapeHtml(msg.qualidade_resposta)}</div>` : ''}
                                ${msg.observacao ? `<div class="msg-quality">Obs: ${escapeHtml(msg.observacao)}</div>` : ''}
                            </div>
                        </div>
                    `).join('')}
                </body>
                </html>
            `;
        }

        function showConversationAnalytics() {
            const conv = appState.state.currentConversation;
            if (!conv) {
                showNotification('Nenhuma conversa selecionada para analytics.', 'warning');
                return;
            }
            const analyticsData = generateConversationAnalytics(conv);
            displayAnalyticsModal(analyticsData, conv.name);
        }

        function generateConversationAnalytics(conversation) {
            const messages = conversation.conversations;
            const sentimentCounts = { Positivo: 0, Negativo: 0, Neutro: 0 };
            const qualityCounts = { boa: 0, regular: 0, ruim: 0, pendente: 0 };
            const wordCounts = { client: 0, ia: 0 };
            const responseTimes = []; // Em segundos, IA respondendo ao cliente

            let lastClientTime = null;

            messages.forEach(msg => {
                // Sentiment e Quality da IA
                const iaSentiment = analyzeSentiment(msg.reposta_ia || '');
                sentimentCounts[iaSentiment]++;
                qualityCounts[msg.qualidade_resposta || 'pendente']++;
                
                // Word counts
                wordCounts.client += countWords(msg.pergunta_cliente);
                wordCounts.ia += countWords(msg.reposta_ia);

                // Response time (simplificado: tempo entre pergunta do cliente e resposta da IA)
                // Assume que pergunta_cliente e reposta_ia são do mesmo "turno"
                if (msg.pergunta_cliente && msg.reposta_ia) {
                    const clientTime = new Date(`${msg.data}T${msg.hora}`); // Hora da pergunta do cliente
                    // Para calcular o tempo de resposta da IA, precisaríamos do timestamp da resposta da IA.
                    // Se 'hora' é o timestamp da interação (pergunta ou resposta), isso é mais complexo.
                    // Vamos assumir que 'hora' em `maqpeças_interacoes` é o timestamp da resposta da IA para essa pergunta.
                    if (lastClientTime) { // Se houver uma pergunta anterior do cliente
                        // Esta lógica precisa ser refinada com timestamps mais precisos
                    }
                    // Para este exemplo, vamos pular o cálculo de tempo de resposta complexo.
                }
            });
            
            return {
                basic: {
                    totalMessages: messages.length * 2, // Cliente + IA
                    totalWords: conversation.totalWords,
                    avgTemperature: conversation.avgTemperature,
                    avgQuality: conversation.avgQuality,
                },
                sentiment: sentimentCounts,
                quality: qualityCounts,
                wordCounts,
                // topKeywords: extractTopKeywords(messages), // Pode ser custoso, adiar se necessário
                // timeDistribution: analyzeTimeDistribution(messages) // Idem
            };
        }
        
        function displayAnalyticsModal(analytics, conversationName) {
            closeAnalyticsModal(); // Fechar modal anterior se existir

            const modal = document.createElement('div');
            modal.id = 'analytics-modal-container'; // ID para fácil remoção
            modal.className = 'analytics-modal'; // Classe para CSS
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeAnalyticsModal()">
                    <div class="modal-content" onclick="event.stopPropagation()">
                        <div class="modal-header">
                            <h2><i class="fas fa-chart-line"></i> Analytics: ${escapeHtml(conversationName)}</h2>
                            <button onclick="closeAnalyticsModal()" class="close-btn" aria-label="Fechar modal"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="modal-body">
                            ${generateAnalyticsHTML(analytics)}
                        </div>
                    </div>
                </div>
            `;
            
            // Adicionar estilos do modal (se não estiverem no CSS principal)
            // ... (código de estilo do modal como no original, mas talvez melhor no CSS)

            document.body.appendChild(modal);
            requestAnimationFrame(() => modal.classList.add('show')); // Para animação
        }

        function generateAnalyticsHTML(analytics) {
            // Melhorar apresentação, talvez com gráficos simples (CSS ou mini-biblioteca)
            return `
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h4><i class="fas fa-info-circle"></i> Resumo</h4>
                        <p><strong>Total de Mensagens (pares):</strong> ${analytics.basic.totalMessages / 2}</p>
                        <p><strong>Total de Palavras:</strong> ${analytics.basic.totalWords}</p>
                        <p><strong>Qualidade Média (IA):</strong> ${analytics.basic.avgQuality !== null ? analytics.basic.avgQuality + '%' : 'N/A'}</p>
                        <p><strong>Complexidade Média (IA):</strong> ${analytics.basic.avgTemperature !== null ? analytics.basic.avgTemperature + '%' : 'N/A'}</p>
                    </div>
                    <div class="analytics-card">
                        <h4><i class="fas fa-smile"></i> Sentimento (Respostas IA)</h4>
                        <p><strong>Positivo:</strong> ${analytics.sentiment.Positivo}</p>
                        <p><strong>Neutro:</strong> ${analytics.sentiment.Neutro}</p>
                        <p><strong>Negativo:</strong> ${analytics.sentiment.Negativo}</p>
                    </div>
                    <div class="analytics-card">
                        <h4><i class="fas fa-check-double"></i> Qualidade (Respostas IA)</h4>
                        <p><strong>Boa:</strong> ${analytics.quality.boa}</p>
                        <p><strong>Regular:</strong> ${analytics.quality.regular}</p>
                        <p><strong>Ruim:</strong> ${analytics.quality.ruim}</p>
                        <p><strong>Pendente:</strong> ${analytics.quality.pendente}</p>
                    </div>
                     <div class="analytics-card">
                        <h4><i class="fas fa-pen-alt"></i> Contagem de Palavras</h4>
                        <p><strong>Cliente:</strong> ${analytics.wordCounts.client}</p>
                        <p><strong>IA:</strong> ${analytics.wordCounts.ia}</p>
                    </div>
                </div>
                <!-- Adicionar gráficos aqui se desejar -->
            `;
        }

        function closeAnalyticsModal() {
            const modal = document.getElementById('analytics-modal-container');
            if (modal) {
                modal.classList.remove('show');
                modal.addEventListener('transitionend', () => modal.remove(), { once: true });
            }
        }

        function allowDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.add('drag-over');
        }
        
        function dragLeave(event) {
            event.currentTarget.classList.remove('drag-over');
        }

        function dragTag(event, tag) {
            event.dataTransfer.setData("text/plain", tag);
            event.dataTransfer.effectAllowed = "move";
        }

        function dropTag(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('drag-over');
            const draggedTag = event.dataTransfer.getData("text/plain");
            const messageId = appState.state.selectedMessage?.id;

            if (draggedTag && messageId) {
                // Lógica para adicionar a tag arrastada, similar a addSuggestedTag
                const currentTags = appState.state.conversationTags[messageId] || [];
                if (!currentTags.includes(draggedTag)) {
                    const newConversationTags = { ...appState.state.conversationTags };
                    newConversationTags[messageId] = [...currentTags, draggedTag];
                    appState.setState({ conversationTags: newConversationTags });
                    renderValidationPanelOptimized();
                    showNotification(`Tag "${draggedTag}" adicionada por arrastar`, 'success');
                }
            }
        }
        
        // A função formatDateForFilename já existe e parece correta.
        // A função fixCodeAnalysis não é usada, pode ser removida.

        function finalizeInitialization() {
            const lastDraftMessageId = localStorage.getItem('last_active_draft_message_id');
            if (lastDraftMessageId) {
                const savedDraft = localStorage.getItem(`validation_draft_${lastDraftMessageId}`);
                if (savedDraft) {
                    try {
                        const draft = JSON.parse(savedDraft);
                        // Considerar tempo do rascunho: if (Date.now() - new Date(draft.timestamp).getTime() < 86400000)
                        showNotification(`Rascunho de validação para mensagem ID ${lastDraftMessageId} encontrado.`, 'info');
                        // Poderia oferecer para carregar o rascunho se a mensagem for selecionada.
                    } catch (error) {
                        localStorage.removeItem(`validation_draft_${lastDraftMessageId}`);
                        localStorage.removeItem('last_active_draft_message_id');
                    }
                }
            }
            
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js') // Certifique-se que sw.js existe e está correto
                    .then(registration => console.log('Service Worker registrado com escopo:', registration.scope))
                    .catch(error => console.log('Falha ao registrar Service Worker:', error));
            }
            
            if ('Notification' in window && Notification.permission === 'granted') {
                console.log('Permissão para notificações já concedida.');
                // new Notification("CRM Pro está ativo!"); // Exemplo, usar com moderação
            } else if ('Notification' in window && Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === "granted") {
                        console.log('Permissão para notificações concedida.');
                        // new Notification("Notificações ativadas para CRM Pro!");
                    }
                });
            }
            
            console.log('CRM Pro finalizou a inicialização.');
            // Remover tela de loading global se houver
            document.body.classList.remove('app-initializing');
        }

        window.addEventListener('load', finalizeInitialization);

        window.addEventListener('error', (event) => {
            console.error('Erro global:', event.error, 'em', event.filename, 'linha', event.lineno);
            showNotification(`Erro inesperado: ${event.message}`, 'error', 6000);
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Promise não tratada:', event.reason);
            const errorMessage = event.reason instanceof Error ? event.reason.message : String(event.reason);
            showNotification(`Erro assíncrono: ${errorMessage}`, 'error', 6000);
        });

        // Adicionar listener para dragleave na dropzone de tags
        document.addEventListener('DOMContentLoaded', () => {
            // A dropzone é criada dinamicamente, então o listener precisa ser delegado
            // ou adicionado após a criação do painel de validação.
            // Uma forma é adicionar ao #validation-content e checar o target.
            const validationContent = document.getElementById('validation-content');
            if (validationContent) {
                validationContent.addEventListener('dragleave', (event) => {
                    if (event.target.id === 'tags-container-dropzone') {
                        event.target.classList.remove('drag-over');
                    }
                });
            }
        });

    </script>
</body>
</html>